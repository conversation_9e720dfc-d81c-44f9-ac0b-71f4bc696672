@echo off
echo 🚀 启动热搜T恤自动化系统 - Web应用版...
echo.

REM 检查Java版本
echo 📋 检查Java版本...
java -version
if %errorlevel% neq 0 (
    echo ❌ Java未安装或未配置到PATH
    echo 💡 请安装JDK 24或更高版本
    pause
    exit /b 1
)

echo.
echo 📋 检查PostgreSQL连接...
REM 这里可以添加PostgreSQL连接检查

echo.
echo 📦 编译项目...
call mvn clean compile
if %errorlevel% neq 0 (
    echo ❌ 编译失败
    pause
    exit /b 1
)

echo.
echo ✅ 编译成功，启动Web应用...
echo.
echo 🌐 应用将在以下地址启动:
echo    - 主页: http://localhost:8080/api
echo    - API文档: http://localhost:8080/api/swagger-ui.html
echo    - 健康检查: http://localhost:8080/api/actuator/health
echo.

REM 启动Spring Boot应用
call mvn spring-boot:run

pause
