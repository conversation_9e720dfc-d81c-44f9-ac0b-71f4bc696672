@echo off
echo 🔍 验证TrendingTopicService新增方法...
echo.

echo 📋 检查方法是否存在...
findstr "getTopicsByStatus" src\main\java\com\hal\service\database\TrendingTopicService.java >nul
if %errorlevel% equ 0 (
    echo ✅ getTopicsByStatus方法已添加
) else (
    echo ❌ getTopicsByStatus方法未找到
)

findstr "getAllTopics" src\main\java\com\hal\service\database\TrendingTopicService.java >nul
if %errorlevel% equ 0 (
    echo ✅ getAllTopics方法已添加
) else (
    echo ❌ getAllTopics方法未找到
)

findstr "findRecent" src\main\java\com\hal\mapper\TrendingTopicMapper.java >nul
if %errorlevel% equ 0 (
    echo ✅ findRecent方法已添加到Mapper
) else (
    echo ❌ findRecent方法未找到
)

echo.
echo 📋 检查Controller中的调用...
findstr "topicService.getTopicsByStatus" src\main\java\com\hal\controller\TopicController.java >nul
if %errorlevel% equ 0 (
    echo ✅ Controller中正确调用getTopicsByStatus
) else (
    echo ❌ Controller中未找到getTopicsByStatus调用
)

echo.
echo 📦 检查方法签名...
findstr "public List<TrendingTopic> getTopicsByStatus(String status)" src\main\java\com\hal\service\database\TrendingTopicService.java >nul
if %errorlevel% equ 0 (
    echo ✅ getTopicsByStatus方法签名正确
) else (
    echo ⚠️ 检查getTopicsByStatus方法签名
)

echo.
echo 📊 方法功能总结:
echo ✅ getTopicsByStatus(String status) - 根据状态查询话题
echo    - 支持具体状态: PENDING, PROCESSING, COMPLETED, FAILED, SKIPPED
echo    - 支持空值查询: null 或 "" 返回所有话题
echo ✅ getAllTopics() - 获取所有话题（最近1000条）
echo ✅ findRecent(int limit) - Mapper中的查询最近话题方法
echo ✅ 增强的getPendingTopics() - 现在使用getTopicsByStatus实现
echo.

echo 🔧 使用示例:
echo    // 查询待处理话题
echo    List^<TrendingTopic^> pending = topicService.getTopicsByStatus("PENDING");
echo.
echo    // 查询所有话题
echo    List^<TrendingTopic^> all = topicService.getTopicsByStatus("");
echo.
echo    // 或者直接调用
echo    List^<TrendingTopic^> all = topicService.getAllTopics();
echo.

echo 🎉 TrendingTopicService方法验证完成！

pause
