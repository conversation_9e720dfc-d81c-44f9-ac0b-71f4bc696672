# 📊 数据库设置指南

## 🎯 概述

热搜T恤自动化系统使用PostgreSQL作为主数据库，通过MyBatis进行数据访问，支持完整的任务记录、热搜分析、成本控制等功能。

## 🛠️ 环境要求

- **PostgreSQL**: 13.0 或更高版本
- **Java**: JDK 24
- **Maven**: 3.8.0 或更高版本

## 📦 PostgreSQL 安装

### Windows

1. 下载PostgreSQL安装包：https://www.postgresql.org/download/windows/
2. 运行安装程序，设置超级用户密码
3. 记住端口号（默认5432）和密码

### macOS

```bash
# 使用Homebrew安装
brew install postgresql
brew services start postgresql

# 创建数据库用户
createuser -s postgres
```

### Linux (Ubuntu/Debian)

```bash
# 安装PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib

# 启动服务
sudo systemctl start postgresql
sudo systemctl enable postgresql

# 设置postgres用户密码
sudo -u postgres psql
\password postgres
\q
```

## 🗄️ 数据库配置

### 1. 创建数据库

```sql
-- 连接到PostgreSQL
psql -U postgres -h localhost

-- 创建数据库
CREATE DATABASE trend_tshirt;

-- 创建用户（可选）
CREATE USER trend_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE trend_tshirt TO trend_user;

-- 退出
\q
```

### 2. 配置连接信息

编辑 `src/main/resources/database.properties`:

```properties
# 数据库连接配置
database.url=*********************************************
database.username=postgres
database.password=your_password

# 连接池配置
database.pool.max-size=10
database.pool.min-idle=2
database.pool.connection-timeout=30000
```

### 3. 验证连接

运行连接测试：

```bash
# Windows
mvn exec:java -Dexec.mainClass="com.hal.database.DatabaseConfig" -Dexec.args="test"

# Linux/Mac
mvn exec:java -Dexec.mainClass="com.hal.database.DatabaseConfig" -Dexec.args="test"
```

## 🏗️ 数据库结构

系统会自动创建以下表：

### 核心业务表

1. **daily_tasks** - 每日任务执行记录
2. **trending_topics** - 热搜话题记录
3. **ai_prompts** - AI提示词记录
4. **generated_images** - 生成图片记录
5. **product_listings** - 商品上架记录
6. **api_usage_logs** - API使用日志
7. **system_configs** - 系统配置
8. **daily_cost_summary** - 每日成本汇总

### 表关系图

```
daily_tasks (1) -----> (N) trending_topics
                              |
                              v
                         ai_prompts (1) -----> (1) generated_images
                              |                        |
                              v                        v
                         product_listings <-----------+
                              |
                              v
                         api_usage_logs
```

## 🚀 初始化数据库

### 自动初始化

系统首次启动时会自动：

1. 检查数据库连接
2. 运行Flyway迁移脚本
3. 创建所有必要的表和索引
4. 插入默认配置数据
5. 创建视图和触发器

### 手动初始化

如果需要手动初始化：

```bash
# 运行数据库迁移
mvn flyway:migrate

# 或者通过应用程序
mvn exec:java -Dexec.mainClass="com.hal.database.DatabaseManager" -Dexec.args="init"
```

## 📊 数据库管理

### 查看数据库状态

```java
DatabaseManager manager = new DatabaseManager();
manager.printStatusReport();
```

### 执行维护任务

```java
// 清理旧数据
manager.performMaintenance();

// 手动清理
DailyTaskService taskService = new DailyTaskService();
taskService.cleanupOldTasks(90); // 保留90天
```

### 备份数据库

```bash
# 创建备份
pg_dump -U postgres -h localhost trend_tshirt > backup_$(date +%Y%m%d).sql

# 恢复备份
psql -U postgres -h localhost trend_tshirt < backup_20241201.sql
```

## 🔧 性能优化

### 索引优化

系统已创建必要的索引：

```sql
-- 时间相关索引
CREATE INDEX idx_daily_tasks_execution_date ON daily_tasks(execution_date);
CREATE INDEX idx_trending_topics_discovered_at ON trending_topics(discovered_at);

-- 文本搜索索引
CREATE INDEX idx_trending_topics_topic_gin ON trending_topics USING gin(topic gin_trgm_ops);
CREATE INDEX idx_ai_prompts_text_gin ON ai_prompts USING gin(prompt_text gin_trgm_ops);
```

### 连接池配置

根据负载调整连接池：

```properties
# 高负载环境
database.pool.max-size=20
database.pool.min-idle=5

# 低负载环境
database.pool.max-size=5
database.pool.min-idle=1
```

## 🔍 监控和诊断

### 查看活跃连接

```sql
SELECT count(*) FROM pg_stat_activity;
```

### 查看慢查询

```sql
-- 启用慢查询日志
ALTER SYSTEM SET log_min_duration_statement = 1000; -- 1秒
SELECT pg_reload_conf();

-- 查看当前运行的查询
SELECT query, state, query_start 
FROM pg_stat_activity 
WHERE state = 'active';
```

### 查看数据库大小

```sql
SELECT pg_size_pretty(pg_database_size('trend_tshirt'));
```

## 🚨 故障排除

### 常见问题

1. **连接被拒绝**
   ```bash
   # 检查PostgreSQL是否运行
   sudo systemctl status postgresql
   
   # 检查端口是否开放
   netstat -an | grep 5432
   ```

2. **认证失败**
   ```bash
   # 检查pg_hba.conf配置
   sudo nano /etc/postgresql/13/main/pg_hba.conf
   
   # 确保有以下行：
   local   all             all                                     md5
   host    all             all             127.0.0.1/32            md5
   ```

3. **内存不足**
   ```sql
   -- 调整内存设置
   ALTER SYSTEM SET shared_buffers = '256MB';
   ALTER SYSTEM SET work_mem = '4MB';
   SELECT pg_reload_conf();
   ```

### 日志查看

```bash
# Ubuntu/Debian
sudo tail -f /var/log/postgresql/postgresql-13-main.log

# CentOS/RHEL
sudo tail -f /var/lib/pgsql/13/data/log/postgresql-*.log

# Windows
# 查看Windows事件日志或PostgreSQL安装目录下的日志文件
```

## 📈 扩展功能

### 启用扩展

```sql
-- 文本搜索扩展
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- UUID扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 统计扩展
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;
```

### 分区表（大数据量时）

```sql
-- 按月分区api_usage_logs表
CREATE TABLE api_usage_logs_y2024m12 PARTITION OF api_usage_logs
FOR VALUES FROM ('2024-12-01') TO ('2025-01-01');
```

## 🔐 安全配置

### 用户权限

```sql
-- 创建只读用户
CREATE USER readonly_user WITH PASSWORD 'readonly_password';
GRANT CONNECT ON DATABASE trend_tshirt TO readonly_user;
GRANT USAGE ON SCHEMA public TO readonly_user;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO readonly_user;

-- 创建应用用户
CREATE USER app_user WITH PASSWORD 'app_password';
GRANT CONNECT ON DATABASE trend_tshirt TO app_user;
GRANT USAGE, CREATE ON SCHEMA public TO app_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO app_user;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO app_user;
```

### SSL配置

```bash
# 生成SSL证书
sudo openssl req -new -x509 -days 365 -nodes -text -out server.crt -keyout server.key

# 配置postgresql.conf
ssl = on
ssl_cert_file = 'server.crt'
ssl_key_file = 'server.key'
```

## 📚 参考资源

- [PostgreSQL官方文档](https://www.postgresql.org/docs/)
- [MyBatis官方文档](https://mybatis.org/mybatis-3/)
- [Flyway迁移指南](https://flywaydb.org/documentation/)
- [HikariCP连接池配置](https://github.com/brettwooldridge/HikariCP)

## 🆘 获取帮助

如果遇到问题：

1. 检查日志文件
2. 验证配置文件
3. 测试数据库连接
4. 查看系统资源使用情况
5. 参考故障排除部分

---

**注意**: 在生产环境中，请确保：
- 定期备份数据库
- 监控性能指标
- 及时更新安全补丁
- 配置适当的访问控制
