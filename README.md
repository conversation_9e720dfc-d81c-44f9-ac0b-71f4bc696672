# 🚀 热搜T恤自动化系统 - 完整版

一个基于AI的自动化系统，每天获取Twitter热搜话题，生成讽刺/搞笑风格的T恤设计，并自动上架到Amazon marketplace。

## ✨ 核心功能

### 🔥 智能热搜获取
- **Twitter API集成**：实时获取热门话题
- **智能过滤**：自动筛选适合的话题
- **多语言支持**：支持中英文热搜

### 🎨 AI图片生成
- **OpenAI DALL-E 3集成**：高质量图片生成
- **专业提示词**：针对不同话题类型的专门提示词
- **T恤优化**：专门针对T恤印刷的设计要求
- **风格多样**：讽刺、搞笑、动漫、卡通等风格

### 🛒 自动商品上架
- **Amazon SP-API集成**：自动上架到Amazon
- **智能商品信息生成**：标题、描述、关键词自动生成
- **图片管理**：自动上传和管理商品图片
- **价格策略**：智能定价算法

### 💰 成本控制
- **配额管理**：每日请求次数限制
- **预算控制**：每日成本上限设置
- **实时监控**：使用统计和成本追踪
- **智能调节**：根据使用情况动态调整

### 🔄 重试机制
- **智能重试**：指数退避重试策略
- **错误分类**：针对不同API的专门重试配置
- **详细日志**：完整的操作记录和错误追踪

### ⏰ 定时任务
- **每日自动执行**：可配置的执行时间
- **任务管理**：启动、停止、监控定时任务
- **灵活调度**：支持自定义执行时间

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Twitter API   │    │   OpenAI API    │    │   Amazon API    │
│   热搜获取      │    │   图片生成      │    │   商品上架      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │              核心业务逻辑层                      │
         │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │
         │  │ 提示词生成  │  │ 商品信息生成 │  │ 成本控制    │ │
         │  └─────────────┘  └─────────────┘  └─────────────┘ │
         └─────────────────────────────────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │              基础设施层                          │
         │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │
         │  │ 重试机制    │  │ 配置管理    │  │ 任务调度    │ │
         │  └─────────────┘  └─────────────┘  └─────────────┘ │
         └─────────────────────────────────────────────────┘
```

## 🚀 快速开始

### 1. 环境要求

- **Java 21+** (推荐Java 24)
- **Maven 3.6+**
- **网络连接** (需要访问各种API)

### 2. 配置API密钥

创建以下配置文件：

#### Twitter API配置 (`C:\me\twitter_keys.txt`)
```
BEARER_TOKEN=your_twitter_bearer_token
API_KEY=your_twitter_api_key
API_SECRET=your_twitter_api_secret
ACCESS_TOKEN=your_twitter_access_token
ACCESS_TOKEN_SECRET=your_twitter_access_token_secret
```

#### OpenAI API配置 (`C:\me\key.txt`)
```
your_openai_api_key
```

#### Amazon API配置 (`C:\me\amazon_config.txt`)
```
SELLER_ID=your_seller_id
CLIENT_ID=your_lwa_client_id
CLIENT_SECRET=your_lwa_client_secret
REFRESH_TOKEN=your_refresh_token
ACCESS_KEY_ID=your_aws_access_key
SECRET_ACCESS_KEY=your_aws_secret_key
REGION=us-east-1
MARKETPLACE_ID=ATVPDKIKX0DER
```

### 3. 编译和运行

```bash
# 编译项目
mvn clean compile

# 运行主应用
mvn exec:java -Dexec.mainClass="com.hal.TrendTShirtApplication"

# 或者运行测试
mvn exec:java -Dexec.mainClass="com.hal.test.EnhancedSystemTest"
```

### 4. 使用Docker (可选)

```bash
# 构建镜像
docker build -t trend-tshirt-app .

# 运行容器
docker run -it --name trend-tshirt trend-tshirt-app
```

## 📋 功能详解

### 🎨 提示词生成系统

系统会根据话题类型自动生成专门的提示词：

- **科技类**：展现人类与科技的讽刺关系
- **环境类**：环保主题的黑色幽默
- **政治类**：政治讽刺（避免具体人物）
- **金融类**：金融市场的搞笑元素
- **娱乐类**：明星文化的讽刺
- **体育类**：体育文化的幽默

### 💰 成本控制策略

- **每日限制**：默认50次OpenAI请求，$10预算
- **智能调节**：根据使用情况动态调整处理数量
- **实时监控**：显示当前使用率和剩余配额
- **预警机制**：接近限制时自动提醒

### 🔄 重试策略

- **OpenAI API**：3次重试，2秒起始延迟，指数退避
- **Twitter API**：3次重试，15秒延迟（应对限流）
- **Amazon API**：5次重试，1秒起始延迟

## ⚙️ 配置选项

系统配置文件 `system_config.properties`：

```properties
# 代理设置
proxy.enabled=true
proxy.host=127.0.0.1
proxy.port=7890

# 成本控制
cost.max_daily_openai_requests=50
cost.max_daily_budget=10.0
cost.openai_standard_cost=0.04
cost.openai_hd_cost=0.08

# 处理限制
processing.max_trends_per_run=5
processing.delay_between_requests=3000
processing.max_retries=3

# 图片设置
image.default_quality=standard
image.size=1024x1024
image.style=vivid

# 调度设置
scheduler.daily_job_time=09:00
scheduler.timezone=Asia/Shanghai
```

## 📊 监控和统计

### 使用统计
- OpenAI API调用次数和成本
- Twitter API调用次数
- Amazon API调用次数
- 成功率和失败率统计

### 实时监控
- 当前使用率
- 剩余配额
- 预计还可处理的热搜数量
- 系统健康状态

## 🧪 测试功能

系统提供完整的测试套件：

```bash
# 运行完整测试
mvn exec:java -Dexec.mainClass="com.hal.test.EnhancedSystemTest"
```

测试包括：
- 成本控制服务测试
- 重试机制测试
- 商品信息生成测试
- API连接测试
- 完整工作流程测试

## 🔧 故障排除

### 常见问题

1. **Java版本问题**
   - 确保使用Java 21+
   - 检查JAVA_HOME环境变量

2. **API配置问题**
   - 验证所有API密钥文件存在且格式正确
   - 检查网络连接和代理设置

3. **权限问题**
   - 确保有写入images目录的权限
   - 检查配置文件的读写权限

4. **内存不足**
   - 增加JVM堆内存：`-Xmx2g`
   - 监控内存使用情况

### 日志和调试

- 系统会输出详细的执行日志
- 错误信息包含具体的失败原因
- 可以通过系统诊断功能检查各项状态

## 📈 性能优化

### 建议配置
- **生产环境**：每日预算$20，最大50次请求
- **测试环境**：每日预算$5，最大10次请求
- **开发环境**：使用模拟模式，避免实际API调用

### 优化策略
- 合理设置请求间隔，避免API限流
- 使用标准质量图片降低成本
- 定期清理生成的图片文件
- 监控成本使用情况，及时调整策略

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请：
1. 查看故障排除部分
2. 运行系统诊断
3. 提交Issue
4. 联系开发团队

---

**注意**：本系统仅供学习和研究使用，请遵守各平台的使用条款和法律法规。
