# 🌐 热搜T恤自动化系统 - Web应用指南

## 🎯 概述

热搜T恤自动化系统Web版本是一个基于Spring Boot的企业级后台服务，提供完整的RESTful API接口，支持前端应用集成和第三方系统调用。

## 🏗️ 技术架构

### 核心技术栈
- **Spring Boot**: 3.2.0 (企业级框架)
- **Spring Web**: RESTful API开发
- **MyBatis**: 数据持久层框架
- **PostgreSQL**: 主数据库
- **Redis**: 缓存和限流
- **Swagger**: API文档自动生成
- **Quartz**: 定时任务调度
- **Maven**: 项目构建管理

### 架构层次
```
┌─────────────────────────────────────┐
│           前端应用/第三方系统          │
└─────────────────┬───────────────────┘
                  │ HTTP/REST API
┌─────────────────▼───────────────────┐
│            Controller层             │ ← 请求处理、参数验证
├─────────────────────────────────────┤
│             Service层               │ ← 业务逻辑处理
├─────────────────────────────────────┤
│             Mapper层                │ ← 数据访问层
├─────────────────────────────────────┤
│            Database层               │ ← PostgreSQL数据库
└─────────────────────────────────────┘
```

## 🚀 快速启动

### 1. 环境准备

**必需环境:**
- JDK 24+
- PostgreSQL 13+
- Maven 3.8+
- Redis 6+ (可选，用于缓存和限流)

**推荐环境:**
- IntelliJ IDEA 2023+
- Postman (API测试)
- DBeaver (数据库管理)

### 2. 数据库配置

编辑 `src/main/resources/application.yml`:

```yaml
spring:
  datasource:
    url: *********************************************
    username: postgres
    password: your_password
```

### 3. 启动应用

```bash
# Windows
start-web-application.bat

# Linux/Mac
./start-web-application.sh

# 或者使用Maven
mvn spring-boot:run
```

### 4. 验证启动

访问以下地址确认启动成功:
- 🏠 **主页**: http://localhost:8080/api
- 📖 **API文档**: http://localhost:8080/api/swagger-ui.html
- 💚 **健康检查**: http://localhost:8080/api/actuator/health

## 📚 API接口文档

### 核心接口模块

#### 1. 任务管理 (`/api/tasks`)

| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/api/tasks/execute` | 立即执行任务 |
| GET | `/api/tasks` | 获取任务列表 |
| GET | `/api/tasks/{id}` | 获取任务详情 |
| GET | `/api/tasks/today` | 获取今日任务 |
| GET | `/api/tasks/statistics` | 获取任务统计 |
| DELETE | `/api/tasks/{id}` | 删除任务 |

**示例请求:**
```bash
# 立即执行任务
curl -X POST http://localhost:8080/api/tasks/execute

# 获取任务列表
curl "http://localhost:8080/api/tasks?pageNum=1&pageSize=10&status=SUCCESS"

# 获取任务统计
curl "http://localhost:8080/api/tasks/statistics?startDate=2024-01-01&endDate=2024-12-31"
```

#### 2. 热搜话题 (`/api/topics`)

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/topics` | 获取话题列表 |
| GET | `/api/topics/{id}` | 获取话题详情 |
| GET | `/api/topics/search` | 搜索话题 |
| GET | `/api/topics/popular` | 获取热门话题 |
| GET | `/api/topics/type-statistics` | 话题类型统计 |
| PUT | `/api/topics/{id}/status` | 更新话题状态 |

**示例请求:**
```bash
# 搜索话题
curl "http://localhost:8080/api/topics/search?keyword=AI&limit=20"

# 获取热门话题
curl "http://localhost:8080/api/topics/popular?days=7&minFrequency=2&limit=10"

# 更新话题状态
curl -X PUT "http://localhost:8080/api/topics/123/status?status=COMPLETED"
```

#### 3. 系统监控 (`/api/actuator`)

| 路径 | 描述 |
|------|------|
| `/api/actuator/health` | 健康检查 |
| `/api/actuator/info` | 应用信息 |
| `/api/actuator/metrics` | 性能指标 |
| `/api/actuator/flyway` | 数据库迁移状态 |

### API响应格式

所有API接口统一使用以下响应格式:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": { ... },
  "timestamp": "2024-12-01 10:30:00",
  "success": true
}
```

**状态码说明:**
- `200`: 成功
- `400`: 请求参数错误
- `404`: 资源不存在
- `429`: 请求频率超限
- `500`: 服务器内部错误

## 🔧 配置说明

### 应用配置 (`application.yml`)

```yaml
# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /api

# 数据库配置
spring:
  datasource:
    url: *********************************************
    username: postgres
    password: password
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5

# Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password: 

# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.hal.entity
  configuration:
    map-underscore-to-camel-case: true

# 自定义配置
app:
  api:
    openai:
      max-daily-requests: 50
      max-daily-budget: 10.00
  processing:
    max-trends-per-run: 5
    delay-between-requests: 3000
```

### 环境配置

支持多环境配置:
- `application.yml` - 通用配置
- `application-dev.yml` - 开发环境
- `application-prod.yml` - 生产环境
- `application-test.yml` - 测试环境

启动时指定环境:
```bash
# 开发环境
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 生产环境
java -jar target/app.jar --spring.profiles.active=prod
```

## 🛡️ 安全特性

### 1. API限流

基于Redis实现的IP级别限流:
- 默认限制: 每分钟100次请求
- 超限返回429状态码
- 响应头包含限流信息

### 2. 参数验证

使用Bean Validation进行参数校验:
```java
@PostMapping("/tasks/execute")
public ResponseEntity<ApiResponse<Object>> executeTask(
    @Valid @RequestBody TaskExecutionRequest request) {
    // ...
}
```

### 3. 异常处理

全局异常处理器统一处理各种异常:
- 参数验证异常
- 数据库访问异常
- 业务逻辑异常
- 系统运行异常

### 4. 日志记录

完整的请求日志记录:
- 请求开始/结束时间
- 请求参数和响应状态
- 异常信息记录
- 性能监控数据

## 📊 监控和运维

### 1. 健康检查

```bash
# 基础健康检查
curl http://localhost:8080/api/actuator/health

# 详细健康信息
curl http://localhost:8080/api/actuator/health/db
curl http://localhost:8080/api/actuator/health/redis
```

### 2. 性能监控

```bash
# JVM指标
curl http://localhost:8080/api/actuator/metrics/jvm.memory.used

# HTTP请求指标
curl http://localhost:8080/api/actuator/metrics/http.server.requests

# 数据库连接池指标
curl http://localhost:8080/api/actuator/metrics/hikaricp.connections
```

### 3. 日志管理

日志配置支持:
- 控制台输出
- 文件滚动记录
- 不同级别的日志
- 结构化日志格式

日志文件位置: `logs/trend-tshirt.log`

## 🧪 测试指南

### 1. 单元测试

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=TaskControllerTest

# 生成测试报告
mvn test jacoco:report
```

### 2. 集成测试

```bash
# 运行集成测试
mvn test -Dtest=*IntegrationTest

# 使用测试配置
mvn test -Dspring.profiles.active=test
```

### 3. API测试

使用Postman或curl进行API测试:

```bash
# 测试任务执行
curl -X POST http://localhost:8080/api/tasks/execute \
  -H "Content-Type: application/json" \
  -d '{"maxTopics": 3, "useHdImages": false}'

# 测试分页查询
curl "http://localhost:8080/api/tasks?pageNum=1&pageSize=5"
```

## 🚀 部署指南

### 1. 开发环境部署

```bash
# 启动开发服务器
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 热重载开发
mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Dspring.devtools.restart.enabled=true"
```

### 2. 生产环境部署

```bash
# 构建生产包
mvn clean package -Pprod

# 运行生产应用
java -jar target/trend-tshirt-web-2.0.jar \
  --spring.profiles.active=prod \
  --server.port=8080
```

### 3. Docker部署

```dockerfile
FROM openjdk:24-jdk-slim
COPY target/trend-tshirt-web-2.0.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

```bash
# 构建镜像
docker build -t trend-tshirt-web .

# 运行容器
docker run -p 8080:8080 -e SPRING_PROFILES_ACTIVE=prod trend-tshirt-web
```

## 📈 性能优化

### 1. 数据库优化

- 使用HikariCP连接池
- 合理配置连接池参数
- 数据库索引优化
- 查询语句优化

### 2. 缓存策略

- Redis缓存热点数据
- MyBatis二级缓存
- HTTP响应缓存
- 静态资源缓存

### 3. 并发处理

- 异步任务处理
- 线程池配置优化
- 数据库连接池调优
- API限流保护

## 🔍 故障排除

### 常见问题

1. **启动失败**
   ```bash
   # 检查端口占用
   netstat -an | grep 8080
   
   # 检查数据库连接
   psql -h localhost -U postgres -d trend_tshirt
   ```

2. **API调用失败**
   ```bash
   # 检查应用状态
   curl http://localhost:8080/api/actuator/health
   
   # 查看日志
   tail -f logs/trend-tshirt.log
   ```

3. **数据库连接问题**
   ```bash
   # 检查PostgreSQL状态
   systemctl status postgresql
   
   # 测试连接
   pg_isready -h localhost -p 5432
   ```

### 日志分析

重要日志关键词:
- `🌐 API请求开始` - 请求开始
- `✅ API请求完成` - 请求成功
- `❌ API请求异常` - 请求异常
- `🚫 API限流触发` - 触发限流

## 📞 技术支持

### 开发团队联系方式
- 📧 技术支持: <EMAIL>
- 🐛 Bug报告: https://github.com/your-repo/issues
- 📚 文档更新: <EMAIL>

### 相关资源
- [Spring Boot官方文档](https://spring.io/projects/spring-boot)
- [MyBatis官方文档](https://mybatis.org/mybatis-3/)
- [PostgreSQL文档](https://www.postgresql.org/docs/)
- [Swagger文档](https://swagger.io/docs/)

---

**注意**: 这是一个企业级Web应用，请确保在生产环境中:
- 配置适当的安全策略
- 设置监控和告警
- 定期备份数据库
- 及时更新安全补丁
