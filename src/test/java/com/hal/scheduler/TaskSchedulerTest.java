package com.hal.scheduler;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.AfterEach;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 任务调度器单元测试
 */
@DisplayName("任务调度器测试")
class TaskSchedulerTest {
    
    private TaskScheduler taskScheduler;
    
    @BeforeEach
    void setUp() throws Exception {
        taskScheduler = new TaskScheduler();
    }
    
    @AfterEach
    void tearDown() throws Exception {
        if (taskScheduler != null) {
            taskScheduler.stopScheduler();
        }
    }
    
    @Test
    @DisplayName("测试调度器初始化")
    void testSchedulerInitialization() {
        assertNotNull(taskScheduler, "调度器应该成功初始化");
    }
    
    @Test
    @DisplayName("测试调度器启动和停止")
    void testSchedulerStartStop() throws Exception {
        // 测试启动
        taskScheduler.startScheduler();
        assertTrue(taskScheduler.isRunning(), "调度器应该在运行中");
        
        // 测试停止
        taskScheduler.stopScheduler();
        assertFalse(taskScheduler.isRunning(), "调度器应该已停止");
    }
    
    @Test
    @DisplayName("测试增强版每日任务调度")
    void testScheduleEnhancedDailyJob() throws Exception {
        // 测试调度任务
        assertDoesNotThrow(() -> {
            taskScheduler.scheduleEnhancedDailyJob("10:30");
        }, "调度增强版每日任务不应该抛出异常");
        
        assertTrue(taskScheduler.isRunning(), "调度任务后调度器应该在运行");
    }
    
    @Test
    @DisplayName("测试无效时间格式")
    void testInvalidTimeFormat() {
        // 测试无效时间格式应该抛出异常
        assertThrows(Exception.class, () -> {
            taskScheduler.scheduleEnhancedDailyJob("25:70"); // 无效时间
        }, "无效时间格式应该抛出异常");
    }
    
    @Test
    @DisplayName("测试重复调度任务")
    void testRescheduleJob() throws Exception {
        // 第一次调度
        taskScheduler.scheduleEnhancedDailyJob("09:00");
        assertTrue(taskScheduler.isRunning(), "第一次调度后应该运行");
        
        // 重新调度
        assertDoesNotThrow(() -> {
            taskScheduler.scheduleEnhancedDailyJob("10:00");
        }, "重新调度不应该抛出异常");
        
        assertTrue(taskScheduler.isRunning(), "重新调度后应该继续运行");
    }
    
    @Test
    @DisplayName("测试调度器状态检查")
    void testSchedulerStatus() throws Exception {
        // 初始状态
        assertFalse(taskScheduler.isRunning(), "初始状态应该未运行");
        
        // 启动后状态
        taskScheduler.startScheduler();
        assertTrue(taskScheduler.isRunning(), "启动后应该在运行");
        
        // 停止后状态
        taskScheduler.stopScheduler();
        assertFalse(taskScheduler.isRunning(), "停止后应该未运行");
    }
}
