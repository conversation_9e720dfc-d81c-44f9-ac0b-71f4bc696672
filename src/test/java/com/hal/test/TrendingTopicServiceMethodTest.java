package com.hal.test;

import com.hal.service.database.TrendingTopicService;
import com.hal.entity.TrendingTopic;
import com.hal.database.DatabaseConfig;

import java.util.List;

/**
 * TrendingTopicService方法测试
 */
public class TrendingTopicServiceMethodTest {
    
    public static void main(String[] args) {
        System.out.println("🧪 测试TrendingTopicService新增方法...");
        
        try {
            // 初始化服务（需要数据库连接）
            DatabaseConfig databaseConfig = new DatabaseConfig();
            TrendingTopicService topicService = new TrendingTopicService(databaseConfig);
            
            // 测试getTopicsByStatus方法
            System.out.println("\n📋 测试getTopicsByStatus方法:");
            
            // 测试查询PENDING状态
            List<TrendingTopic> pendingTopics = topicService.getTopicsByStatus("PENDING");
            System.out.println("✅ getTopicsByStatus(\"PENDING\"): " + pendingTopics.size() + " 个话题");
            
            // 测试查询COMPLETED状态
            List<TrendingTopic> completedTopics = topicService.getTopicsByStatus("COMPLETED");
            System.out.println("✅ getTopicsByStatus(\"COMPLETED\"): " + completedTopics.size() + " 个话题");
            
            // 测试查询空字符串（应该返回所有话题）
            List<TrendingTopic> allTopics = topicService.getTopicsByStatus("");
            System.out.println("✅ getTopicsByStatus(\"\"): " + allTopics.size() + " 个话题");
            
            // 测试查询null（应该返回所有话题）
            List<TrendingTopic> allTopicsNull = topicService.getTopicsByStatus(null);
            System.out.println("✅ getTopicsByStatus(null): " + allTopicsNull.size() + " 个话题");
            
            // 验证空字符串和null的结果应该相同
            if (allTopics.size() == allTopicsNull.size()) {
                System.out.println("✅ 空字符串和null查询结果一致");
            } else {
                System.out.println("❌ 空字符串和null查询结果不一致");
            }
            
            // 测试getAllTopics方法
            System.out.println("\n📋 测试getAllTopics方法:");
            List<TrendingTopic> allTopicsDirect = topicService.getAllTopics();
            System.out.println("✅ getAllTopics(): " + allTopicsDirect.size() + " 个话题");
            
            // 验证getAllTopics和getTopicsByStatus("")的结果应该相同
            if (allTopics.size() == allTopicsDirect.size()) {
                System.out.println("✅ getAllTopics和getTopicsByStatus(\"\")结果一致");
            } else {
                System.out.println("❌ getAllTopics和getTopicsByStatus(\"\")结果不一致");
            }
            
            // 显示一些话题示例（如果有的话）
            if (!allTopics.isEmpty()) {
                System.out.println("\n📋 话题示例:");
                for (int i = 0; i < Math.min(3, allTopics.size()); i++) {
                    TrendingTopic topic = allTopics.get(i);
                    System.out.println("   " + (i + 1) + ". " + topic.getTopic() + 
                                     " (状态: " + topic.getStatus() + 
                                     ", 类型: " + topic.getTopicType() + ")");
                }
            }
            
            // 测试各种状态的查询
            System.out.println("\n📋 测试各种状态查询:");
            String[] statuses = {"PENDING", "PROCESSING", "COMPLETED", "FAILED", "SKIPPED"};
            for (String status : statuses) {
                List<TrendingTopic> topics = topicService.getTopicsByStatus(status);
                System.out.println("   " + status + ": " + topics.size() + " 个话题");
            }
            
            System.out.println("\n🎉 所有方法测试完成！");
            
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            System.err.println("💡 提示: 请确保数据库已正确配置并可访问");
            e.printStackTrace();
        }
    }
}
