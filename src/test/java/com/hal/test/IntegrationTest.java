package com.hal.test;

import com.hal.service.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Disabled;
import static org.junit.jupiter.api.Assertions.*;

import java.util.List;

/**
 * 集成测试类
 * 测试各个服务之间的集成
 */
@DisplayName("系统集成测试")
class IntegrationTest {
    
    private TwitterTrendService twitterService;
    private OpenAIImageService openAIService;
    private AmazonListingService amazonService;
    private ProductInfoGenerator productGenerator;
    private CostControlService costControl;
    
    @BeforeEach
    void setUp() {
        twitterService = new TwitterTrendService();
        openAIService = new OpenAIImageService();
        amazonService = new AmazonListingService();
        productGenerator = new ProductInfoGenerator();
        costControl = new CostControlService();
        
        // 设置代理（如果需要）
        twitterService.setProxy("127.0.0.1", 7890);
        openAIService.setProxy("127.0.0.1", 7890);
        amazonService.setProxy("127.0.0.1", 7890);
    }
    
    @Test
    @DisplayName("测试服务初始化")
    void testServiceInitialization() {
        assertNotNull(twitterService, "Twitter服务应该初始化成功");
        assertNotNull(openAIService, "OpenAI服务应该初始化成功");
        assertNotNull(amazonService, "Amazon服务应该初始化成功");
        assertNotNull(productGenerator, "商品信息生成器应该初始化成功");
        assertNotNull(costControl, "成本控制服务应该初始化成功");
    }
    
    @Test
    @DisplayName("测试Twitter到商品信息的完整流程")
    void testTwitterToProductInfoFlow() throws Exception {
        // 1. 获取热搜（模拟）
        List<String> mockTrends = List.of("人工智能", "Climate Change", "Cryptocurrency");
        
        // 2. 为每个热搜生成商品信息
        for (String trend : mockTrends) {
            AmazonListingService.ProductInfo product = productGenerator.generateProductInfo(
                trend,
                "https://example.com/mock_" + trend.hashCode() + ".jpg",
                "/local/mock_" + trend.hashCode() + ".jpg"
            );
            
            assertNotNull(product, "商品信息不应该为null");
            assertEquals(trend, product.getTopic(), "话题应该匹配");
            assertNotNull(product.getTitle(), "标题不应该为null");
            assertNotNull(product.getDescription(), "描述不应该为null");
            assertNotNull(product.getKeywords(), "关键词不应该为null");
            assertNotNull(product.getPrice(), "价格不应该为null");
        }
    }
    
    @Test
    @DisplayName("测试成本控制与服务集成")
    void testCostControlIntegration() {
        // 测试成本控制与OpenAI服务的集成
        assertTrue(costControl.canMakeOpenAIRequest(false), "应该允许标准质量请求");
        assertTrue(costControl.canMakeOpenAIRequest(true), "应该允许高清质量请求");
        
        // 模拟记录API调用
        costControl.recordOpenAIRequest(false, true);
        costControl.recordTwitterRequest(true);
        costControl.recordAmazonRequest(true);
        
        // 验证统计信息
        CostControlService.UsageStats stats = costControl.getTodayUsageStats();
        assertTrue(stats.openAIRequests > 0, "OpenAI请求计数应该增加");
        assertTrue(stats.twitterRequests > 0, "Twitter请求计数应该增加");
        assertTrue(stats.amazonRequests > 0, "Amazon请求计数应该增加");
    }
    
    @Test
    @DisplayName("测试重试机制与服务集成")
    void testRetryServiceIntegration() throws Exception {
        // 测试重试机制与商品信息生成的集成
        String result = RetryService.executeWithRetry(() -> {
            AmazonListingService.ProductInfo product = productGenerator.generateProductInfo(
                "Test Topic",
                "https://example.com/test.jpg",
                "/local/test.jpg"
            );
            return product.getTitle();
        }, RetryService.openAIRetryConfig());
        
        assertNotNull(result, "重试机制应该返回结果");
        assertFalse(result.isEmpty(), "结果不应该为空");
    }
    
    @Test
    @Disabled("需要实际API密钥才能运行")
    @DisplayName("测试Twitter API集成")
    void testTwitterAPIIntegration() throws Exception {
        List<String> trends = twitterService.getTrendingTopics();
        
        assertNotNull(trends, "热搜列表不应该为null");
        // 注意：实际结果可能为空，取决于API状态
    }
    
    @Test
    @Disabled("需要实际API密钥和费用才能运行")
    @DisplayName("测试OpenAI API集成")
    void testOpenAIAPIIntegration() throws Exception {
        // 检查成本控制
        if (!costControl.canMakeOpenAIRequest(false)) {
            System.out.println("跳过OpenAI测试：已达到成本限制");
            return;
        }
        
        String imageUrl = openAIService.generateTShirtImage("Test Topic", false);
        
        assertNotNull(imageUrl, "图片URL不应该为null");
        assertTrue(imageUrl.startsWith("http"), "应该返回有效的URL");
    }
    
    @Test
    @Disabled("需要实际Amazon配置才能运行")
    @DisplayName("测试Amazon API集成")
    void testAmazonAPIIntegration() throws Exception {
        AmazonListingService.ProductInfo testProduct = productGenerator.generateProductInfo(
            "Integration Test",
            "https://example.com/integration_test.jpg",
            "/local/integration_test.jpg"
        );
        
        String listingId = amazonService.listProduct(testProduct);
        
        // 注意：在测试模式下可能返回null
        // assertNotNull(listingId, "商品上架应该返回listing ID");
    }
    
    @Test
    @DisplayName("测试完整工作流程模拟")
    void testCompleteWorkflowSimulation() {
        // 模拟完整的工作流程，不调用实际API
        
        // 1. 模拟获取热搜
        List<String> mockTrends = List.of("AI Revolution", "Green Energy", "Space Exploration");
        
        // 2. 处理每个热搜
        for (String trend : mockTrends) {
            // 检查成本控制
            assertTrue(costControl.canMakeOpenAIRequest(false), "应该允许处理热搜");
            
            // 生成商品信息
            AmazonListingService.ProductInfo product = productGenerator.generateProductInfo(
                trend,
                "https://example.com/mock_" + trend.hashCode() + ".jpg",
                "/local/mock_" + trend.hashCode() + ".jpg"
            );
            
            // 验证商品信息
            assertNotNull(product, "商品信息应该生成成功");
            assertTrue(product.getTitle().length() > 10, "标题应该有足够长度");
            assertTrue(product.getDescription().length() > 50, "描述应该有足够长度");
            assertTrue(product.getKeywords().size() >= 5, "关键词应该足够多");
            
            // 模拟记录API使用
            costControl.recordOpenAIRequest(false, true);
            costControl.recordAmazonRequest(true);
        }
        
        // 验证最终状态
        CostControlService.UsageStats stats = costControl.getTodayUsageStats();
        assertEquals(mockTrends.size(), stats.openAIRequests, "OpenAI请求数应该匹配");
        assertEquals(mockTrends.size(), stats.amazonRequests, "Amazon请求数应该匹配");
    }
    
    @Test
    @DisplayName("测试错误处理集成")
    void testErrorHandlingIntegration() {
        // 测试各种错误情况下的集成行为
        
        // 1. 测试空话题处理
        assertDoesNotThrow(() -> {
            AmazonListingService.ProductInfo product = productGenerator.generateProductInfo(
                "",
                "https://example.com/empty.jpg",
                "/local/empty.jpg"
            );
            assertNotNull(product, "即使话题为空也应该生成商品信息");
        }, "空话题不应该导致异常");
        
        // 2. 测试特殊字符话题处理
        assertDoesNotThrow(() -> {
            AmazonListingService.ProductInfo product = productGenerator.generateProductInfo(
                "Test@#$%^&*()",
                "https://example.com/special.jpg",
                "/local/special.jpg"
            );
            assertNotNull(product, "特殊字符话题应该能正常处理");
        }, "特殊字符不应该导致异常");
    }
}
