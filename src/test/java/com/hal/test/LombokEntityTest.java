package com.hal.test;

import com.hal.entity.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDateTime;
import java.math.BigDecimal;

/**
 * Lombok实体类测试
 */
@DisplayName("Lombok实体类测试")
class LombokEntityTest {

    @Test
    @DisplayName("测试DailyTask实体")
    void testDailyTaskEntity() {
        DailyTask task = new DailyTask();
        task.setId(1L);
        task.setExecutionDate(LocalDateTime.now());
        task.setStatus("SUCCESS");
        task.setTotalTrends(10);
        task.setProcessedTrends(8);
        task.setSuccessfulImages(6);
        task.setSuccessfulListings(4);
        task.setTotalCost(0.32);
        
        assertEquals(1L, task.getId());
        assertEquals("SUCCESS", task.getStatus());
        assertEquals(10, task.getTotalTrends());
        assertEquals(8, task.getProcessedTrends());
        assertEquals(6, task.getSuccessfulImages());
        assertEquals(4, task.getSuccessfulListings());
        assertEquals(0.32, task.getTotalCost(), 0.001);
        
        assertNotNull(task.toString());
        System.out.println("✅ DailyTask实体测试通过");
    }

    @Test
    @DisplayName("测试TrendingTopic实体")
    void testTrendingTopicEntity() {
        TrendingTopic topic = new TrendingTopic("AI Technology", 1, "Twitter");
        
        assertNotNull(topic.getTopic());
        assertEquals("AI Technology", topic.getTopic());
        assertEquals(1, topic.getRanking());
        assertEquals("Twitter", topic.getSource());
        assertEquals("PENDING", topic.getStatus());
        assertNotNull(topic.getDiscoveredAt());
        assertNotNull(topic.getCreatedAt());
        
        // 测试setter
        topic.setTopicType("technology");
        topic.setLanguage("en");
        assertEquals("technology", topic.getTopicType());
        assertEquals("en", topic.getLanguage());
        
        assertNotNull(topic.toString());
        System.out.println("✅ TrendingTopic实体测试通过");
    }

    @Test
    @DisplayName("测试AiPrompt实体")
    void testAiPromptEntity() {
        AiPrompt prompt = new AiPrompt(1L, "Create a funny t-shirt design about AI", "COMPLETE");
        
        assertEquals(1L, prompt.getTopicId());
        assertEquals("Create a funny t-shirt design about AI", prompt.getPromptText());
        assertEquals("COMPLETE", prompt.getPromptType());
        assertNotNull(prompt.getCreatedAt());
        
        // 测试setter
        prompt.setModel("dall-e-3");
        prompt.setQuality("hd");
        prompt.setTokenCount(50);
        
        assertEquals("dall-e-3", prompt.getModel());
        assertEquals("hd", prompt.getQuality());
        assertEquals(50, prompt.getTokenCount());
        
        assertNotNull(prompt.toString());
        System.out.println("✅ AiPrompt实体测试通过");
    }

    @Test
    @DisplayName("测试GeneratedImage实体")
    void testGeneratedImageEntity() {
        GeneratedImage image = new GeneratedImage(1L, 1L, "https://example.com/image.png");
        
        assertEquals(1L, image.getTopicId());
        assertEquals(1L, image.getPromptId());
        assertEquals("https://example.com/image.png", image.getOriginalUrl());
        assertEquals("GENERATED", image.getStatus());
        assertNotNull(image.getGeneratedAt());
        assertNotNull(image.getCreatedAt());
        
        // 测试setter
        image.setFileName("ai_tshirt_001.png");
        image.setFileSize(1024000L);
        image.setGenerationCost(0.04);
        
        assertEquals("ai_tshirt_001.png", image.getFileName());
        assertEquals(1024000L, image.getFileSize());
        assertEquals(0.04, image.getGenerationCost(), 0.001);
        
        assertNotNull(image.toString());
        System.out.println("✅ GeneratedImage实体测试通过");
    }

    @Test
    @DisplayName("测试ProductListing实体")
    void testProductListingEntity() {
        ProductListing listing = new ProductListing(1L, 1L, "AI Funny T-Shirt", new BigDecimal("19.99"));
        
        assertEquals(1L, listing.getTopicId());
        assertEquals(1L, listing.getImageId());
        assertEquals("AI Funny T-Shirt", listing.getTitle());
        assertEquals(new BigDecimal("19.99"), listing.getPrice());
        assertEquals("PENDING", listing.getStatus());
        assertEquals("USD", listing.getCurrency());
        assertEquals("Amazon", listing.getMarketplace());
        assertEquals("US", listing.getRegion());
        assertEquals(0, listing.getViewCount());
        assertEquals(0, listing.getOrderCount());
        assertEquals(BigDecimal.ZERO, listing.getTotalRevenue());
        
        assertNotNull(listing.getCreatedAt());
        assertNotNull(listing.getUpdatedAt());
        assertNotNull(listing.toString());
        System.out.println("✅ ProductListing实体测试通过");
    }

    @Test
    @DisplayName("测试ApiUsageLog实体")
    void testApiUsageLogEntity() {
        ApiUsageLog log = new ApiUsageLog("OpenAI", "/v1/images/generations", "POST");
        
        assertEquals("OpenAI", log.getApiProvider());
        assertEquals("/v1/images/generations", log.getApiEndpoint());
        assertEquals("POST", log.getRequestMethod());
        assertEquals("PENDING", log.getResponseStatus());
        assertEquals(0, log.getRetryCount());
        assertEquals("USD", log.getCurrency());
        assertNotNull(log.getRequestAt());
        assertNotNull(log.getCreatedAt());
        
        // 测试setter
        log.setResponseStatus("SUCCESS");
        log.setHttpStatusCode(200);
        log.setResponseTimeMs(1500L);
        log.setCost(0.04);
        
        assertEquals("SUCCESS", log.getResponseStatus());
        assertEquals(200, log.getHttpStatusCode());
        assertEquals(1500L, log.getResponseTimeMs());
        assertEquals(0.04, log.getCost(), 0.001);
        
        assertNotNull(log.toString());
        System.out.println("✅ ApiUsageLog实体测试通过");
    }

    @Test
    @DisplayName("测试统计实体类")
    void testStatisticsEntities() {
        // 测试TaskStatistics
        TaskStatistics taskStats = new TaskStatistics(10L, 8L, 1L, 1L, 0.32, 5000.0);
        assertEquals(10L, taskStats.getTotalTasks());
        assertEquals(8L, taskStats.getSuccessfulTasks());
        assertEquals(1L, taskStats.getFailedTasks());
        assertEquals(1L, taskStats.getPartialTasks());
        assertEquals(0.32, taskStats.getTotalCost(), 0.001);
        assertEquals(5000.0, taskStats.getAvgExecutionTime(), 0.001);
        
        // 测试TopicFrequency
        TopicFrequency frequency = new TopicFrequency("AI Technology", 5L, LocalDateTime.now());
        assertEquals("AI Technology", frequency.getTopic());
        assertEquals(5L, frequency.getFrequency());
        assertNotNull(frequency.getLastSeen());
        
        // 测试TopicTypeStats
        TopicTypeStats typeStats = new TopicTypeStats("technology", 10L, 8L);
        assertEquals("technology", typeStats.getTopicType());
        assertEquals(10L, typeStats.getCount());
        assertEquals(8L, typeStats.getCompletedCount());
        assertEquals(80.0, typeStats.getCompletionRate(), 0.001);
        
        // 测试StorageUsageStats
        StorageUsageStats storageStats = new StorageUsageStats(100L, 1024000L, 80L, 20L, 10240.0);
        assertEquals(100L, storageStats.getTotalImages());
        assertEquals(1024000L, storageStats.getTotalSize());
        assertEquals(80L, storageStats.getLocalStored());
        assertEquals(20L, storageStats.getCloudStored());
        assertEquals(10240.0, storageStats.getAvgFileSize(), 0.001);
        assertEquals("1000.0 KB", storageStats.getFormattedTotalSize());
        
        System.out.println("✅ 统计实体类测试通过");
    }

    @Test
    @DisplayName("测试实体类equals和hashCode")
    void testEntityEqualsAndHashCode() {
        // 测试DailyTask
        DailyTask task1 = new DailyTask();
        task1.setId(1L);
        task1.setStatus("SUCCESS");
        
        DailyTask task2 = new DailyTask();
        task2.setId(1L);
        task2.setStatus("SUCCESS");
        
        assertEquals(task1, task2);
        assertEquals(task1.hashCode(), task2.hashCode());
        
        // 测试TrendingTopic
        TrendingTopic topic1 = new TrendingTopic("AI", 1, "Twitter");
        TrendingTopic topic2 = new TrendingTopic("AI", 1, "Twitter");
        
        assertEquals(topic1, topic2);
        assertEquals(topic1.hashCode(), topic2.hashCode());
        
        System.out.println("✅ 实体类equals和hashCode测试通过");
    }
}
