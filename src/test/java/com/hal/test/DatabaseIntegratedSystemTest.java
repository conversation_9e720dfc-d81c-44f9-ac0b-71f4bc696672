package com.hal.test;

import com.hal.database.DatabaseManager;
import com.hal.service.database.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据库集成系统测试
 */
@DisplayName("数据库集成系统测试")
class DatabaseIntegratedSystemTest {
    
    private DatabaseManager databaseManager;
    private DailyTaskService taskService;
    private TrendingTopicService topicService;
    private SystemConfigService configService;
    private ApiUsageLogService apiLogService;
    
    @BeforeEach
    void setUp() {
        databaseManager = new DatabaseManager();
        taskService = new DailyTaskService();
        topicService = new TrendingTopicService();
        configService = new SystemConfigService();
        apiLogService = new ApiUsageLogService();
    }
    
    @Test
    @DisplayName("测试数据库连接")
    void testDatabaseConnection() {
        assertTrue(databaseManager.testConnection(), "数据库应该能够连接");
    }
    
    @Test
    @DisplayName("测试任务服务")
    void testTaskService() {
        // 创建任务
        var task = taskService.createTask(LocalDateTime.now());
        assertNotNull(task, "任务应该创建成功");
        assertNotNull(task.getId(), "任务ID不应该为null");
        
        // 查询任务
        var foundTask = taskService.getTaskById(task.getId());
        assertNotNull(foundTask, "应该能够查询到任务");
        assertEquals(task.getId(), foundTask.getId(), "任务ID应该匹配");
    }
    
    @Test
    @DisplayName("测试话题服务")
    void testTopicService() {
        // 创建任务
        var task = taskService.createTask(LocalDateTime.now());
        
        // 保存话题
        List<String> topics = List.of("测试话题1", "测试话题2", "测试话题3");
        var savedTopics = topicService.saveTrendingTopics(task.getId(), topics, "Test");
        
        assertFalse(savedTopics.isEmpty(), "应该保存了话题");
        assertEquals(topics.size(), savedTopics.size(), "保存的话题数量应该匹配");
        
        // 查询话题
        var foundTopics = topicService.getTopicsByTaskId(task.getId());
        assertEquals(savedTopics.size(), foundTopics.size(), "查询的话题数量应该匹配");
    }
    
    @Test
    @DisplayName("测试系统配置服务")
    void testConfigService() {
        String testKey = "test.config.key";
        String testValue = "test_value";
        
        // 设置配置
        configService.setConfig(testKey, testValue);
        
        // 获取配置
        String retrievedValue = configService.getConfig(testKey);
        assertEquals(testValue, retrievedValue, "配置值应该匹配");
        
        // 删除配置
        configService.deleteConfig(testKey);
        String deletedValue = configService.getConfig(testKey);
        assertNull(deletedValue, "删除后配置应该为null");
    }
    
    @Test
    @DisplayName("测试API日志服务")
    void testApiLogService() {
        // 创建任务
        var task = taskService.createTask(LocalDateTime.now());
        
        // 记录API请求
        var log = apiLogService.logRequestStart(task.getId(), "TestAPI", "/test", "GET", "{}");
        assertNotNull(log, "API日志应该创建成功");
        
        // 完成请求
        apiLogService.logRequestComplete(log.getId(), "SUCCESS", 200, "OK", 1000L, 0.01);
        
        // 查询日志
        var logs = apiLogService.getTodayUsageStats();
        assertFalse(logs.isEmpty(), "应该有API使用统计");
    }
    
    @Test
    @DisplayName("测试完整工作流程")
    void testCompleteWorkflow() {
        // 1. 创建任务
        var task = taskService.createTask(LocalDateTime.now());
        taskService.startTask(task.getId());
        
        // 2. 保存话题
        List<String> topics = List.of("AI Technology", "Climate Change");
        var savedTopics = topicService.saveTrendingTopics(task.getId(), topics, "Twitter");
        
        // 3. 更新话题状态
        for (var topic : savedTopics) {
            topicService.updateTopicStatus(topic.getId(), "PROCESSING");
        }
        
        // 4. 记录API使用
        for (var topic : savedTopics) {
            var log = apiLogService.logRequestStart(task.getId(), "OpenAI", "/v1/images/generations", "POST", "{}");
            apiLogService.logRequestComplete(log.getId(), "SUCCESS", 200, "Image generated", 5000L, 0.04);
            
            topicService.updateTopicStatus(topic.getId(), "COMPLETED");
        }
        
        // 5. 完成任务
        taskService.completeTask(task.getId(), topics.size(), topics.size(), topics.size(), topics.size(), 0.08, 10000L);
        
        // 验证结果
        var completedTask = taskService.getTaskById(task.getId());
        assertEquals("SUCCESS", completedTask.getStatus(), "任务状态应该为成功");
        assertEquals(topics.size(), completedTask.getTotalTrends(), "话题数量应该匹配");
        
        var completedTopics = topicService.getTopicsByTaskId(task.getId());
        assertTrue(completedTopics.stream().allMatch(t -> "COMPLETED".equals(t.getStatus())), 
                  "所有话题都应该完成");
    }
    
    @Test
    @DisplayName("测试数据库状态")
    void testDatabaseStatus() {
        var status = databaseManager.getDatabaseStatus();
        assertNotNull(status, "数据库状态不应该为null");
        assertTrue(status.isConnected, "数据库应该连接正常");
        assertNotNull(status.databaseVersion, "数据库版本不应该为null");
        assertNotNull(status.tableStats, "表统计不应该为null");
    }
    
    @Test
    @DisplayName("测试配置缓存")
    void testConfigCache() {
        // 设置配置
        configService.setConfig("cache.test", "value1");
        
        // 第一次获取（从数据库）
        String value1 = configService.getConfig("cache.test");
        assertEquals("value1", value1, "第一次获取应该成功");
        
        // 第二次获取（从缓存）
        String value2 = configService.getConfig("cache.test");
        assertEquals("value1", value2, "第二次获取应该从缓存返回相同值");
        
        // 刷新缓存
        configService.refreshCache();
        String value3 = configService.getConfig("cache.test");
        assertEquals("value1", value3, "刷新缓存后应该仍然能获取到值");
    }
}
