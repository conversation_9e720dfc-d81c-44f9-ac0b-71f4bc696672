package com.hal.test;

import com.hal.service.CostControlService;

/**
 * 简单的成本控制方法测试
 */
public class CostControlMethodTest {
    
    public static void main(String[] args) {
        System.out.println("🧪 测试CostControlService新增方法...");
        
        try {
            CostControlService costControl = new CostControlService();
            
            // 测试getMaxTopicsCanProcess方法
            System.out.println("\n📋 测试getMaxTopicsCanProcess方法:");
            
            int maxTopicsDefault = costControl.getMaxTopicsCanProcess();
            System.out.println("✅ getMaxTopicsCanProcess(): " + maxTopicsDefault);
            
            int maxTopicsStandard = costControl.getMaxTopicsCanProcess(false);
            System.out.println("✅ getMaxTopicsCanProcess(false): " + maxTopicsStandard);
            
            int maxTopicsHD = costControl.getMaxTopicsCanProcess(true);
            System.out.println("✅ getMaxTopicsCanProcess(true): " + maxTopicsHD);
            
            // 验证逻辑
            if (maxTopicsDefault == maxTopicsStandard) {
                System.out.println("✅ 默认方法与标准质量结果一致");
            } else {
                System.out.println("❌ 默认方法与标准质量结果不一致");
            }
            
            if (maxTopicsHD <= maxTopicsStandard) {
                System.out.println("✅ 高清质量可处理话题数 <= 标准质量");
            } else {
                System.out.println("❌ 高清质量可处理话题数 > 标准质量");
            }
            
            // 测试canProcessTopics方法
            System.out.println("\n📋 测试canProcessTopics方法:");
            
            boolean canProcess1 = costControl.canProcessTopics(1, false);
            System.out.println("✅ canProcessTopics(1, false): " + canProcess1);
            
            boolean canProcessMax = costControl.canProcessTopics(maxTopicsStandard, false);
            System.out.println("✅ canProcessTopics(" + maxTopicsStandard + ", false): " + canProcessMax);
            
            boolean canProcessOver = costControl.canProcessTopics(maxTopicsStandard + 1, false);
            System.out.println("✅ canProcessTopics(" + (maxTopicsStandard + 1) + ", false): " + canProcessOver);
            
            // 测试getQuotaInfo方法
            System.out.println("\n📋 测试getQuotaInfo方法:");
            
            CostControlService.QuotaInfo quotaInfo = costControl.getQuotaInfo();
            System.out.println("✅ QuotaInfo创建成功");
            System.out.println("   剩余请求数: " + quotaInfo.remainingRequests);
            System.out.println("   最大请求数: " + quotaInfo.maxRequests);
            System.out.println("   剩余预算: $" + String.format("%.2f", quotaInfo.remainingBudget));
            System.out.println("   最大预算: $" + String.format("%.2f", quotaInfo.maxBudget));
            System.out.println("   标准质量最大话题数: " + quotaInfo.maxTopicsStandard);
            System.out.println("   高清质量最大话题数: " + quotaInfo.maxTopicsHD);
            System.out.println("   请求使用率: " + String.format("%.1f", quotaInfo.getRequestUsagePercentage()) + "%");
            System.out.println("   预算使用率: " + String.format("%.1f", quotaInfo.getBudgetUsagePercentage()) + "%");
            
            // 测试打印统计（包含新的配额信息）
            System.out.println("\n📋 测试增强的printUsageStats方法:");
            costControl.printUsageStats();
            
            System.out.println("\n🎉 所有新增方法测试完成！");
            
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
