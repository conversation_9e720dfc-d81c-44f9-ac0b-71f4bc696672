package com.hal.test;

import com.hal.service.AmazonListingService;
import com.hal.service.OpenAIImageService;
import com.hal.service.TwitterTrendService;

import java.util.List;

/**
 * 系统测试类
 * 用于测试各个组件的功能
 */
public class SystemTest {
    
    public static void main(String[] args) {
        System.out.println("🧪 开始系统组件测试");
        System.out.println("=".repeat(50));
        
        testTwitterService();
        testImageService();
        testAmazonService();
        
        System.out.println("\n✅ 所有测试完成");
    }

    /**
     * 测试推特服务
     */
    private static void testTwitterService() {
        System.out.println("\n📱 测试推特热搜服务...");
        try {
            TwitterTrendService service = new TwitterTrendService();
            service.setProxy("127.0.0.1", 7890); // 设置代理
            
            List<String> trends = service.getTrendingTopics();
            System.out.println("✅ 推特服务测试成功，获取到 " + trends.size() + " 个热搜");
            
        } catch (Exception e) {
            System.err.println("❌ 推特服务测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试图片生成服务
     */
    private static void testImageService() {
        System.out.println("\n🎨 测试OpenAI图片生成服务...");
        try {
            OpenAIImageService service = new OpenAIImageService();
            service.setProxy("127.0.0.1", 7890); // 设置代理
            
            // 注意：这里只是测试连接，不实际生成图片以节省费用
            System.out.println("⚠️ 跳过实际图片生成以节省API费用");
            System.out.println("✅ OpenAI服务配置测试通过");
            
        } catch (Exception e) {
            System.err.println("❌ OpenAI服务测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试亚马逊服务
     */
    private static void testAmazonService() {
        System.out.println("\n🛒 测试亚马逊商品上架服务...");
        try {
            AmazonListingService service = new AmazonListingService();
            service.setProxy("127.0.0.1", 7890); // 设置代理
            
            // 创建测试商品信息
            AmazonListingService.ProductInfo testProduct = new AmazonListingService.ProductInfo();
            testProduct.setTopic("测试话题");
            testProduct.setTitle("测试T恤设计");
            testProduct.setDescription("这是一个测试商品描述");
            testProduct.setImageUrl("https://example.com/test.jpg");
            testProduct.setLocalImagePath("/path/to/test.jpg");
            
            // 注意：这里只是测试配置，不实际上架商品
            System.out.println("⚠️ 跳过实际商品上架以避免创建测试商品");
            System.out.println("✅ 亚马逊服务配置测试通过");
            
        } catch (Exception e) {
            System.err.println("❌ 亚马逊服务测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试完整工作流程
     */
    public static void testCompleteWorkflow() {
        System.out.println("\n🔄 测试完整工作流程...");
        
        try {
            // 1. 获取热搜
            TwitterTrendService twitterService = new TwitterTrendService();
            twitterService.setProxy("127.0.0.1", 7890);
            List<String> trends = twitterService.getTrendingTopics();
            
            if (trends.isEmpty()) {
                System.out.println("❌ 未获取到热搜，无法继续测试");
                return;
            }
            
            System.out.println("✅ 获取到 " + trends.size() + " 个热搜");
            
            // 2. 选择第一个热搜进行测试
            String testTopic = trends.get(0);
            System.out.println("📝 测试话题: " + testTopic);
            
            // 3. 模拟图片生成（不实际调用API）
            System.out.println("🎨 模拟图片生成...");
            String mockImageUrl = "https://example.com/generated_" + System.currentTimeMillis() + ".jpg";
            System.out.println("✅ 模拟图片生成完成: " + mockImageUrl);
            
            // 4. 创建商品信息
            AmazonListingService.ProductInfo product = new AmazonListingService.ProductInfo();
            product.setTopic(testTopic);
            product.setTitle("热搜T恤: " + testTopic);
            product.setDescription("基于热搜话题 '" + testTopic + "' 设计的讽刺搞笑T恤");
            product.setImageUrl(mockImageUrl);
            product.setLocalImagePath("/mock/path/image.jpg");
            
            System.out.println("✅ 商品信息创建完成");
            
            // 5. 模拟上架（不实际调用API）
            System.out.println("🛒 模拟商品上架...");
            String mockListingId = "MOCK_" + System.currentTimeMillis();
            System.out.println("✅ 模拟上架完成，商品ID: " + mockListingId);
            
            System.out.println("🎉 完整工作流程测试成功！");
            
        } catch (Exception e) {
            System.err.println("❌ 完整工作流程测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
