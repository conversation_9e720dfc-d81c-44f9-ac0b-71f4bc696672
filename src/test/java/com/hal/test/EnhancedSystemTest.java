package com.hal.test;

import com.hal.service.*;
import com.hal.scheduler.EnhancedDailyTrendJob;

import java.util.Arrays;
import java.util.List;
import java.util.Scanner;

/**
 * 增强版系统测试类
 * 测试所有新增功能和改进
 */
public class EnhancedSystemTest {
    
    public static void main(String[] args) {
        System.out.println("🧪 增强版系统功能测试");
        System.out.println("=".repeat(50));
        
        Scanner scanner = new Scanner(System.in);
        
        while (true) {
            showMenu();
            System.out.print("请选择测试项目 (0-8): ");
            
            try {
                int choice = scanner.nextInt();
                scanner.nextLine(); // 消费换行符
                
                switch (choice) {
                    case 0 -> {
                        System.out.println("👋 测试结束，再见！");
                        return;
                    }
                    case 1 -> testCostControlService();
                    case 2 -> testRetryService();
                    case 3 -> testProductInfoGenerator();
                    case 4 -> testEnhancedOpenAIService();
                    case 5 -> testTwitterService();
                    case 6 -> testAmazonService();
                    case 7 -> testCompleteWorkflow();
                    case 8 -> testEnhancedDailyJob();
                    default -> System.out.println("❌ 无效选择，请重试");
                }
                
                System.out.println("\n" + "=".repeat(50));
                
            } catch (Exception e) {
                System.err.println("❌ 测试执行出错: " + e.getMessage());
                e.printStackTrace();
                scanner.nextLine(); // 清理输入缓冲区
            }
        }
    }
    
    private static void showMenu() {
        System.out.println("\n📋 测试菜单:");
        System.out.println("1. 测试成本控制服务");
        System.out.println("2. 测试重试机制");
        System.out.println("3. 测试商品信息生成器");
        System.out.println("4. 测试增强版OpenAI服务");
        System.out.println("5. 测试Twitter服务");
        System.out.println("6. 测试Amazon服务");
        System.out.println("7. 测试完整工作流程");
        System.out.println("8. 测试增强版定时任务");
        System.out.println("0. 退出");
        System.out.println("-".repeat(30));
    }
    
    /**
     * 测试成本控制服务
     */
    private static void testCostControlService() {
        System.out.println("\n💰 测试成本控制服务...");
        
        try {
            CostControlService costControl = new CostControlService();
            
            // 显示当前状态
            costControl.printUsageStats();
            
            // 测试检查功能
            System.out.println("\n🔍 测试API调用检查:");
            System.out.println("可以调用OpenAI (标准): " + costControl.canMakeOpenAIRequest(false));
            System.out.println("可以调用OpenAI (高清): " + costControl.canMakeOpenAIRequest(true));
            System.out.println("可以调用Twitter: " + costControl.canMakeTwitterRequest());
            System.out.println("可以调用Amazon: " + costControl.canMakeAmazonRequest());
            
            // 模拟一些API调用
            System.out.println("\n📝 模拟API调用记录:");
            costControl.recordOpenAIRequest(false, true);
            costControl.recordTwitterRequest(true);
            costControl.recordAmazonRequest(true);
            
            // 显示更新后的状态
            costControl.printUsageStats();
            
            System.out.println("✅ 成本控制服务测试完成");
            
        } catch (Exception e) {
            System.err.println("❌ 成本控制服务测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试重试机制
     */
    private static void testRetryService() {
        System.out.println("\n🔄 测试重试机制...");
        
        try {
            // 测试成功的操作
            System.out.println("测试成功操作:");
            String result = RetryService.executeWithDetailedLogging(
                "测试成功操作",
                () -> "操作成功!",
                new RetryService.RetryConfig().maxAttempts(3)
            );
            System.out.println("结果: " + result);
            
            // 测试失败后成功的操作
            System.out.println("\n测试失败后成功的操作:");
            final int[] attemptCount = {0};
            String result2 = RetryService.executeWithDetailedLogging(
                "测试重试操作",
                () -> {
                    attemptCount[0]++;
                    if (attemptCount[0] < 3) {
                        throw new RuntimeException("模拟失败 (尝试 " + attemptCount[0] + ")");
                    }
                    return "第3次尝试成功!";
                },
                new RetryService.RetryConfig().maxAttempts(5).delay(500)
            );
            System.out.println("结果: " + result2);
            
            System.out.println("✅ 重试机制测试完成");
            
        } catch (Exception e) {
            System.err.println("❌ 重试机制测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试商品信息生成器
     */
    private static void testProductInfoGenerator() {
        System.out.println("\n📦 测试商品信息生成器...");
        
        try {
            ProductInfoGenerator generator = new ProductInfoGenerator();
            
            // 测试不同类型的话题
            List<String> testTopics = Arrays.asList(
                "人工智能",
                "Climate Change",
                "Cryptocurrency",
                "Celebrity Drama",
                "Sports Championship"
            );
            
            for (String topic : testTopics) {
                System.out.println("\n🏷️ 话题: " + topic);
                AmazonListingService.ProductInfo product = generator.generateProductInfo(
                    topic, 
                    "https://example.com/image.jpg", 
                    "/local/path/image.jpg"
                );
                
                System.out.println("标题: " + product.getTitle());
                System.out.println("价格: $" + product.getPrice());
                System.out.println("关键词数量: " + product.getKeywords().size());
                System.out.println("描述长度: " + product.getDescription().length() + " 字符");
            }
            
            System.out.println("✅ 商品信息生成器测试完成");
            
        } catch (Exception e) {
            System.err.println("❌ 商品信息生成器测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试增强版OpenAI服务
     */
    private static void testEnhancedOpenAIService() {
        System.out.println("\n🎨 测试增强版OpenAI服务...");
        
        try {
            OpenAIImageService service = new OpenAIImageService();
            service.setProxy("127.0.0.1", 7890);
            
            // 显示当前成本状态
            service.getCostControl().printUsageStats();
            
            // 测试提示词生成（不实际调用API）
            System.out.println("\n📝 测试提示词生成:");
            System.out.println("这里只测试提示词生成逻辑，不实际调用OpenAI API");
            
            // 如果用户确认，可以测试实际API调用
            Scanner scanner = new Scanner(System.in);
            System.out.print("是否测试实际的图片生成? (y/N): ");
            String response = scanner.nextLine().trim().toLowerCase();
            
            if ("y".equals(response) || "yes".equals(response)) {
                System.out.println("正在生成测试图片...");
                String imageUrl = service.generateTShirtImage("AI Technology", false);
                System.out.println("✅ 图片生成成功: " + imageUrl);
                
                // 显示更新后的成本状态
                service.getCostControl().printUsageStats();
            } else {
                System.out.println("跳过实际API调用测试");
            }
            
            System.out.println("✅ 增强版OpenAI服务测试完成");
            
        } catch (Exception e) {
            System.err.println("❌ 增强版OpenAI服务测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试Twitter服务
     */
    private static void testTwitterService() {
        System.out.println("\n📱 测试Twitter服务...");
        
        try {
            TwitterTrendService service = new TwitterTrendService();
            service.setProxy("127.0.0.1", 7890);
            
            List<String> trends = service.getTrendingTopics();
            System.out.println("✅ 获取到 " + trends.size() + " 个热搜话题");
            
            for (int i = 0; i < Math.min(trends.size(), 5); i++) {
                System.out.println((i + 1) + ". " + trends.get(i));
            }
            
            System.out.println("✅ Twitter服务测试完成");
            
        } catch (Exception e) {
            System.err.println("❌ Twitter服务测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试Amazon服务
     */
    private static void testAmazonService() {
        System.out.println("\n🛒 测试Amazon服务...");
        
        try {
            AmazonListingService service = new AmazonListingService();
            service.setProxy("127.0.0.1", 7890);
            
            // 创建测试商品
            ProductInfoGenerator generator = new ProductInfoGenerator();
            AmazonListingService.ProductInfo testProduct = generator.generateProductInfo(
                "Test Topic", 
                "https://example.com/test.jpg", 
                "/local/test.jpg"
            );
            
            // 测试商品上架（通常会使用模拟模式）
            String listingId = service.listProduct(testProduct);
            
            if (listingId != null) {
                System.out.println("✅ 商品上架测试成功，Listing ID: " + listingId);
            } else {
                System.out.println("⚠️ 商品上架返回null，可能使用了模拟模式");
            }
            
            System.out.println("✅ Amazon服务测试完成");
            
        } catch (Exception e) {
            System.err.println("❌ Amazon服务测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试完整工作流程
     */
    private static void testCompleteWorkflow() {
        System.out.println("\n🔄 测试完整工作流程...");
        
        try {
            // 1. 获取热搜
            TwitterTrendService twitterService = new TwitterTrendService();
            twitterService.setProxy("127.0.0.1", 7890);
            List<String> trends = twitterService.getTrendingTopics();
            
            if (trends.isEmpty()) {
                System.out.println("❌ 未获取到热搜，无法继续测试");
                return;
            }
            
            // 2. 选择第一个热搜进行测试
            String testTopic = trends.get(0);
            System.out.println("📝 测试话题: " + testTopic);
            
            // 3. 生成商品信息（不实际调用OpenAI API）
            ProductInfoGenerator generator = new ProductInfoGenerator();
            AmazonListingService.ProductInfo product = generator.generateProductInfo(
                testTopic, 
                "https://example.com/generated.jpg", 
                "/local/generated.jpg"
            );
            
            System.out.println("✅ 商品信息生成完成:");
            System.out.println("- 标题: " + product.getTitle());
            System.out.println("- 价格: $" + product.getPrice());
            
            // 4. 模拟上架
            AmazonListingService amazonService = new AmazonListingService();
            amazonService.setProxy("127.0.0.1", 7890);
            String listingId = amazonService.listProduct(product);
            
            System.out.println("✅ 完整工作流程测试完成");
            
        } catch (Exception e) {
            System.err.println("❌ 完整工作流程测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试增强版定时任务
     */
    private static void testEnhancedDailyJob() {
        System.out.println("\n⏰ 测试增强版定时任务...");
        
        try {
            EnhancedDailyTrendJob job = new EnhancedDailyTrendJob();
            
            System.out.println("⚠️ 注意：这将执行完整的定时任务，可能会消耗API配额");
            Scanner scanner = new Scanner(System.in);
            System.out.print("确认执行? (y/N): ");
            String response = scanner.nextLine().trim().toLowerCase();
            
            if ("y".equals(response) || "yes".equals(response)) {
                job.execute(null);
                System.out.println("✅ 增强版定时任务测试完成");
            } else {
                System.out.println("跳过定时任务执行测试");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 增强版定时任务测试失败: " + e.getMessage());
        }
    }
}
