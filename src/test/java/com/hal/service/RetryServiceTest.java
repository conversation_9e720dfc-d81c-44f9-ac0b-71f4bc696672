package com.hal.service;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * 重试服务单元测试
 */
@DisplayName("重试服务测试")
class RetryServiceTest {
    
    @Test
    @DisplayName("测试成功操作不需要重试")
    void testSuccessfulOperationNoRetry() throws Exception {
        String result = RetryService.executeWithRetry(() -> "成功", new RetryService.RetryConfig());
        assertEquals("成功", result, "成功操作应该返回正确结果");
    }
    
    @Test
    @DisplayName("测试失败后成功的重试")
    void testRetryAfterFailure() throws Exception {
        AtomicInteger attemptCount = new AtomicInteger(0);
        
        String result = RetryService.executeWithRetry(() -> {
            int attempt = attemptCount.incrementAndGet();
            if (attempt < 3) {
                throw new RuntimeException("模拟失败 " + attempt);
            }
            return "第3次成功";
        }, new RetryService.RetryConfig().maxAttempts(5).delay(100));
        
        assertEquals("第3次成功", result, "重试后应该成功");
        assertEquals(3, attemptCount.get(), "应该尝试了3次");
    }
    
    @Test
    @DisplayName("测试达到最大重试次数后失败")
    void testMaxRetriesExceeded() {
        AtomicInteger attemptCount = new AtomicInteger(0);
        
        Exception exception = assertThrows(RuntimeException.class, () -> {
            RetryService.executeWithRetry(() -> {
                attemptCount.incrementAndGet();
                throw new RuntimeException("持续失败");
            }, new RetryService.RetryConfig().maxAttempts(3).delay(50));
        });
        
        assertEquals("持续失败", exception.getMessage(), "应该抛出最后一次的异常");
        assertEquals(3, attemptCount.get(), "应该尝试了最大次数");
    }
    
    @Test
    @DisplayName("测试重试条件过滤")
    void testRetryCondition() {
        AtomicInteger attemptCount = new AtomicInteger(0);
        
        // 只对包含"retry"的异常进行重试
        Exception exception = assertThrows(RuntimeException.class, () -> {
            RetryService.executeWithRetry(() -> {
                attemptCount.incrementAndGet();
                throw new RuntimeException("不重试的错误");
            }, new RetryService.RetryConfig()
                .maxAttempts(3)
                .delay(50)
                .retryOn(ex -> ex.getMessage().contains("retry")));
        });
        
        assertEquals("不重试的错误", exception.getMessage(), "应该抛出原始异常");
        assertEquals(1, attemptCount.get(), "不满足重试条件时只应该尝试1次");
    }
    
    @Test
    @DisplayName("测试OpenAI重试配置")
    void testOpenAIRetryConfig() {
        RetryService.RetryConfig config = RetryService.openAIRetryConfig();
        
        assertEquals(3, config.getMaxAttempts(), "OpenAI配置应该有3次最大尝试");
        assertEquals(2000, config.getDelayMs(), "OpenAI配置应该有2秒延迟");
        assertEquals(2.0, config.getBackoffMultiplier(), "OpenAI配置应该有2.0倍退避");
        
        // 测试重试条件
        assertTrue(config.getRetryCondition().test(new RuntimeException("timeout")), 
                  "应该对timeout错误重试");
        assertTrue(config.getRetryCondition().test(new RuntimeException("500")), 
                  "应该对500错误重试");
        assertFalse(config.getRetryCondition().test(new RuntimeException("invalid key")), 
                   "不应该对认证错误重试");
    }
    
    @Test
    @DisplayName("测试Twitter重试配置")
    void testTwitterRetryConfig() {
        RetryService.RetryConfig config = RetryService.twitterRetryConfig();
        
        assertEquals(3, config.getMaxAttempts(), "Twitter配置应该有3次最大尝试");
        assertEquals(15000, config.getDelayMs(), "Twitter配置应该有15秒延迟");
        assertEquals(1.5, config.getBackoffMultiplier(), "Twitter配置应该有1.5倍退避");
        
        // 测试重试条件
        assertTrue(config.getRetryCondition().test(new RuntimeException("429")), 
                  "应该对限流错误重试");
        assertTrue(config.getRetryCondition().test(new RuntimeException("503")), 
                  "应该对服务不可用错误重试");
    }
    
    @Test
    @DisplayName("测试Amazon重试配置")
    void testAmazonRetryConfig() {
        RetryService.RetryConfig config = RetryService.amazonRetryConfig();
        
        assertEquals(5, config.getMaxAttempts(), "Amazon配置应该有5次最大尝试");
        assertEquals(1000, config.getDelayMs(), "Amazon配置应该有1秒延迟");
        assertEquals(2.0, config.getBackoffMultiplier(), "Amazon配置应该有2.0倍退避");
        
        // 测试重试条件
        assertTrue(config.getRetryCondition().test(new RuntimeException("throttl")), 
                  "应该对限流错误重试");
        assertTrue(config.getRetryCondition().test(new RuntimeException("connection")), 
                  "应该对连接错误重试");
    }
    
    @Test
    @DisplayName("测试详细日志记录")
    void testDetailedLogging() {
        assertDoesNotThrow(() -> {
            String result = RetryService.executeWithDetailedLogging(
                "测试操作",
                () -> "操作成功",
                new RetryService.RetryConfig().maxAttempts(1)
            );
            assertEquals("操作成功", result, "详细日志记录应该返回正确结果");
        }, "详细日志记录不应该抛出异常");
    }
    
    @Test
    @DisplayName("测试指数退避延迟")
    void testExponentialBackoff() throws Exception {
        AtomicInteger attemptCount = new AtomicInteger(0);
        long startTime = System.currentTimeMillis();
        
        try {
            RetryService.executeWithRetry(() -> {
                int attempt = attemptCount.incrementAndGet();
                if (attempt <= 2) {
                    throw new RuntimeException("失败 " + attempt);
                }
                return "成功";
            }, new RetryService.RetryConfig()
                .maxAttempts(3)
                .delay(100)
                .backoffMultiplier(2.0));
        } catch (Exception e) {
            // 忽略异常，我们只关心时间
        }
        
        long duration = System.currentTimeMillis() - startTime;
        
        // 第一次延迟100ms，第二次延迟200ms，总共至少300ms
        assertTrue(duration >= 250, "指数退避应该产生适当的延迟");
        assertEquals(3, attemptCount.get(), "应该尝试了3次");
    }
}
