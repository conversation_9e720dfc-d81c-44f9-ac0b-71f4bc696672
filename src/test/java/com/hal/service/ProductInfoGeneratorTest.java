package com.hal.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 商品信息生成器单元测试
 */
@DisplayName("商品信息生成器测试")
class ProductInfoGeneratorTest {
    
    private ProductInfoGenerator generator;
    
    @BeforeEach
    void setUp() {
        generator = new ProductInfoGenerator();
    }
    
    @Test
    @DisplayName("测试基本商品信息生成")
    void testGenerateProductInfo() {
        String topic = "人工智能";
        String imageUrl = "https://example.com/test.jpg";
        String localPath = "/local/test.jpg";
        
        AmazonListingService.ProductInfo product = generator.generateProductInfo(topic, imageUrl, localPath);
        
        assertNotNull(product, "生成的商品信息不应该为null");
        assertEquals(topic, product.getTopic(), "话题应该匹配");
        assertEquals(imageUrl, product.getImageUrl(), "图片URL应该匹配");
        assertEquals(localPath, product.getLocalImagePath(), "本地路径应该匹配");
        
        assertNotNull(product.getTitle(), "标题不应该为null");
        assertFalse(product.getTitle().isEmpty(), "标题不应该为空");
        
        assertNotNull(product.getDescription(), "描述不应该为null");
        assertFalse(product.getDescription().isEmpty(), "描述不应该为空");
        
        assertNotNull(product.getKeywords(), "关键词不应该为null");
        assertFalse(product.getKeywords().isEmpty(), "关键词列表不应该为空");
        
        assertNotNull(product.getPrice(), "价格不应该为null");
        assertTrue(product.getPrice().doubleValue() > 0, "价格应该大于0");
    }
    
    @ParameterizedTest
    @DisplayName("测试不同类型话题的商品信息生成")
    @ValueSource(strings = {
        "人工智能", "AI Technology", "Climate Change", "气候变化",
        "Cryptocurrency", "加密货币", "Celebrity Drama", "明星八卦",
        "Sports Championship", "体育比赛", "Politics", "政治"
    })
    void testGenerateProductInfoForDifferentTopics(String topic) {
        AmazonListingService.ProductInfo product = generator.generateProductInfo(
            topic, 
            "https://example.com/test.jpg", 
            "/local/test.jpg"
        );
        
        assertNotNull(product, "商品信息不应该为null");
        assertTrue(product.getTitle().contains(topic) || 
                  product.getDescription().contains(topic), 
                  "标题或描述应该包含话题关键词");
        
        // 验证关键词数量合理
        assertTrue(product.getKeywords().size() >= 5, "关键词数量应该至少5个");
        assertTrue(product.getKeywords().size() <= 20, "关键词数量不应该超过20个");
        
        // 验证价格范围
        double price = product.getPrice().doubleValue();
        assertTrue(price >= 19.99 && price <= 29.99, "价格应该在合理范围内");
    }
    
    @Test
    @DisplayName("测试中文话题处理")
    void testChineseTopicHandling() {
        String chineseTopic = "人工智能发展";
        AmazonListingService.ProductInfo product = generator.generateProductInfo(
            chineseTopic, 
            "https://example.com/test.jpg", 
            "/local/test.jpg"
        );
        
        assertNotNull(product, "中文话题商品信息不应该为null");
        assertTrue(product.getTitle().length() > 0, "中文话题标题应该有内容");
        assertTrue(product.getDescription().length() > 0, "中文话题描述应该有内容");
    }
    
    @Test
    @DisplayName("测试英文话题处理")
    void testEnglishTopicHandling() {
        String englishTopic = "Artificial Intelligence";
        AmazonListingService.ProductInfo product = generator.generateProductInfo(
            englishTopic, 
            "https://example.com/test.jpg", 
            "/local/test.jpg"
        );
        
        assertNotNull(product, "英文话题商品信息不应该为null");
        assertTrue(product.getTitle().length() > 0, "英文话题标题应该有内容");
        assertTrue(product.getDescription().length() > 0, "英文话题描述应该有内容");
    }
    
    @Test
    @DisplayName("测试价格生成的随机性")
    void testPriceRandomness() {
        String topic = "Test Topic";
        
        // 生成多个商品，检查价格是否有变化
        AmazonListingService.ProductInfo product1 = generator.generateProductInfo(topic, "url1", "path1");
        AmazonListingService.ProductInfo product2 = generator.generateProductInfo(topic, "url2", "path2");
        AmazonListingService.ProductInfo product3 = generator.generateProductInfo(topic, "url3", "path3");
        
        // 至少有一个价格不同（随机性测试）
        boolean pricesVary = !product1.getPrice().equals(product2.getPrice()) || 
                           !product2.getPrice().equals(product3.getPrice());
        
        // 注意：由于随机性，这个测试可能偶尔失败，但概率很低
        assertTrue(pricesVary, "价格应该有随机性变化");
    }
    
    @Test
    @DisplayName("测试关键词生成的完整性")
    void testKeywordGeneration() {
        String topic = "Technology Innovation";
        AmazonListingService.ProductInfo product = generator.generateProductInfo(
            topic, 
            "https://example.com/test.jpg", 
            "/local/test.jpg"
        );
        
        // 检查是否包含基础关键词
        assertTrue(product.getKeywords().stream().anyMatch(k -> k.contains("shirt") || k.contains("tee")), 
                  "应该包含T恤相关关键词");
        
        // 检查是否包含话题相关关键词
        assertTrue(product.getKeywords().stream().anyMatch(k -> 
                  k.toLowerCase().contains("tech") || k.toLowerCase().contains("innovation")), 
                  "应该包含话题相关关键词");
    }
}
