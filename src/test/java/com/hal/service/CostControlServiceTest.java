package com.hal.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 成本控制服务单元测试
 */
@DisplayName("成本控制服务测试")
class CostControlServiceTest {

    private CostControlService costControlService;

    @BeforeEach
    void setUp() {
        costControlService = new CostControlService();
    }

    @Test
    @DisplayName("测试OpenAI请求检查")
    void testCanMakeOpenAIRequest() {
        // 测试标准质量请求
        assertTrue(costControlService.canMakeOpenAIRequest(false),
                  "应该允许标准质量的OpenAI请求");

        // 测试高清质量请求
        assertTrue(costControlService.canMakeOpenAIRequest(true),
                  "应该允许高清质量的OpenAI请求");
    }

    @Test
    @DisplayName("测试Twitter请求检查")
    void testCanMakeTwitterRequest() {
        assertTrue(costControlService.canMakeTwitterRequest(),
                  "应该允许Twitter请求");
    }

    @Test
    @DisplayName("测试Amazon请求检查")
    void testCanMakeAmazonRequest() {
        assertTrue(costControlService.canMakeAmazonRequest(),
                  "应该允许Amazon请求");
    }

    @Test
    @DisplayName("测试API调用记录")
    void testRecordAPIRequests() {
        // 记录成功的OpenAI请求
        assertDoesNotThrow(() -> {
            costControlService.recordOpenAIRequest(false, true);
        }, "记录OpenAI请求不应该抛出异常");

        // 记录成功的Twitter请求
        assertDoesNotThrow(() -> {
            costControlService.recordTwitterRequest(true);
        }, "记录Twitter请求不应该抛出异常");

        // 记录成功的Amazon请求
        assertDoesNotThrow(() -> {
            costControlService.recordAmazonRequest(true);
        }, "记录Amazon请求不应该抛出异常");
    }

    @Test
    @DisplayName("测试使用统计获取")
    void testGetUsageStats() {
        CostControlService.UsageStats stats = costControlService.getTodayUsageStats();

        assertNotNull(stats, "使用统计不应该为null");
        assertTrue(stats.maxOpenAIRequests > 0, "最大OpenAI请求数应该大于0");
        assertTrue(stats.maxDailyBudget > 0, "最大每日预算应该大于0");
        assertTrue(stats.openAIRequests >= 0, "OpenAI请求数应该大于等于0");
        assertTrue(stats.dailyCost >= 0, "每日成本应该大于等于0");
    }

    @Test
    @DisplayName("测试使用率计算")
    void testUsagePercentageCalculation() {
        CostControlService.UsageStats stats = costControlService.getTodayUsageStats();

        double openAIPercentage = stats.getOpenAIUsagePercentage();
        double costPercentage = stats.getCostUsagePercentage();

        assertTrue(openAIPercentage >= 0 && openAIPercentage <= 100,
                  "OpenAI使用率应该在0-100%之间");
        assertTrue(costPercentage >= 0 && costPercentage <= 100,
                  "成本使用率应该在0-100%之间");
    }

    @Test
    @DisplayName("测试获取最大可处理话题数")
    void testGetMaxTopicsCanProcess() {
        // 测试默认方法（标准质量）
        int maxTopicsStandard = costControlService.getMaxTopicsCanProcess();
        assertTrue(maxTopicsStandard >= 0, "最大可处理话题数应该大于等于0");

        // 测试指定质量方法
        int maxTopicsStandardExplicit = costControlService.getMaxTopicsCanProcess(false);
        int maxTopicsHD = costControlService.getMaxTopicsCanProcess(true);

        assertTrue(maxTopicsStandardExplicit >= 0, "标准质量最大可处理话题数应该大于等于0");
        assertTrue(maxTopicsHD >= 0, "高清质量最大可处理话题数应该大于等于0");

        // 高清质量成本更高，所以可处理的话题数应该小于等于标准质量
        assertTrue(maxTopicsHD <= maxTopicsStandardExplicit,
                  "高清质量可处理话题数应该小于等于标准质量");

        // 默认方法应该与标准质量的结果相同
        assertEquals(maxTopicsStandard, maxTopicsStandardExplicit,
                    "默认方法应该与标准质量的结果相同");
    }

    @Test
    @DisplayName("测试检查是否可以处理指定数量的话题")
    void testCanProcessTopics() {
        int maxTopics = costControlService.getMaxTopicsCanProcess(false);

        // 如果有可用配额，测试小于等于最大值的情况
        if (maxTopics > 0) {
            assertTrue(costControlService.canProcessTopics(1, false),
                      "应该能够处理1个话题");
            assertTrue(costControlService.canProcessTopics(maxTopics, false),
                      "应该能够处理最大数量的话题");
            assertFalse(costControlService.canProcessTopics(maxTopics + 1, false),
                       "不应该能够处理超过最大数量的话题");
        }

        // 测试负数情况
        assertFalse(costControlService.canProcessTopics(-1, false),
                   "不应该能够处理负数个话题");
    }

    @Test
    @DisplayName("测试获取配额信息")
    void testGetQuotaInfo() {
        CostControlService.QuotaInfo quotaInfo = costControlService.getQuotaInfo();

        assertNotNull(quotaInfo, "配额信息不应该为null");
        assertTrue(quotaInfo.maxRequests > 0, "最大请求数应该大于0");
        assertTrue(quotaInfo.maxBudget > 0, "最大预算应该大于0");
        assertTrue(quotaInfo.remainingRequests >= 0, "剩余请求数应该大于等于0");
        assertTrue(quotaInfo.remainingBudget >= 0, "剩余预算应该大于等于0");
        assertTrue(quotaInfo.maxTopicsStandard >= 0, "标准质量最大话题数应该大于等于0");
        assertTrue(quotaInfo.maxTopicsHD >= 0, "高清质量最大话题数应该大于等于0");

        // 测试使用率计算
        double requestUsage = quotaInfo.getRequestUsagePercentage();
        double budgetUsage = quotaInfo.getBudgetUsagePercentage();

        assertTrue(requestUsage >= 0 && requestUsage <= 100,
                  "请求使用率应该在0-100%之间");
        assertTrue(budgetUsage >= 0 && budgetUsage <= 100,
                  "预算使用率应该在0-100%之间");
    }
}
