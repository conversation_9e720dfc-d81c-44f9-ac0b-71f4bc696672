# ????????

# ????
proxy.enabled=true
proxy.host=127.0.0.1
proxy.port=7890

# ????????????????
cost.max_daily_openai_requests=5
cost.max_daily_budget=1.0
cost.openai_standard_cost=0.04
cost.openai_hd_cost=0.08

# ??????????
processing.max_trends_per_run=2
processing.delay_between_requests=1000
processing.max_retries=2

# ????
image.default_quality=standard
image.size=1024x1024
image.style=vivid

# ??????????
files.image_directory=test_images
files.quota_file=test_quota_usage.txt

# ????
scheduler.daily_job_time=10:00
scheduler.timezone=Asia/Shanghai

# ??????
test.mode=true
test.skip_actual_api_calls=true
test.use_mock_data=true
