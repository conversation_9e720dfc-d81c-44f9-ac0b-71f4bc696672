<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 测试文件输出 -->
    <appender name="FILE" class="ch.qos.logback.core.FileAppender">
        <file>test_logs/test.log</file>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 根日志级别 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </root>

    <!-- 测试相关的日志级别 -->
    <logger name="com.hal.test" level="DEBUG" />
    <logger name="com.hal.service" level="DEBUG" />
    
    <!-- 第三方库日志级别 -->
    <logger name="org.apache.http" level="WARN" />
    <logger name="okhttp3" level="WARN" />
    <logger name="org.quartz" level="WARN" />
</configuration>
