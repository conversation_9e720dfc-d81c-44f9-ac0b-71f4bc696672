-- 热搜T恤自动化系统 - 初始数据库结构
-- 版本: V1
-- 描述: 创建核心业务表

-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm"; -- 用于文本相似度搜索

-- 1. 每日任务执行记录表
CREATE TABLE daily_tasks (
    id BIGSERIAL PRIMARY KEY,
    execution_date TIMESTAMP NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('PENDING', 'RUNNING', 'SUCCESS', 'FAILED', 'PARTIAL')),
    total_trends INTEGER DEFAULT 0,
    processed_trends INTEGER DEFAULT 0,
    successful_images INTEGER DEFAULT 0,
    successful_listings INTEGER DEFAULT 0,
    total_cost DECIMAL(10,4) DEFAULT 0.00,
    error_message TEXT,
    execution_time_ms BIGINT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 2. 热搜话题表
CREATE TABLE trending_topics (
    id BIGSERIAL PRIMARY KEY,
    task_id BIGINT REFERENCES daily_tasks(id) ON DELETE CASCADE,
    topic VARCHAR(500) NOT NULL,
    topic_type VARCHAR(50) DEFAULT 'general' CHECK (topic_type IN ('technology', 'environment', 'politics', 'finance', 'entertainment', 'sports', 'general')),
    ranking INTEGER,
    source VARCHAR(50) NOT NULL DEFAULT 'Twitter',
    language VARCHAR(10) DEFAULT 'en',
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'SKIPPED')),
    discovered_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 3. AI提示词表
CREATE TABLE ai_prompts (
    id BIGSERIAL PRIMARY KEY,
    topic_id BIGINT REFERENCES trending_topics(id) ON DELETE CASCADE,
    prompt_text TEXT NOT NULL,
    prompt_type VARCHAR(50) NOT NULL DEFAULT 'COMPLETE' CHECK (prompt_type IN ('BASE', 'STYLE', 'TECHNICAL', 'COMPLETE')),
    topic_type VARCHAR(50),
    language VARCHAR(10) DEFAULT 'en',
    model VARCHAR(50) DEFAULT 'dall-e-3',
    quality VARCHAR(20) DEFAULT 'standard' CHECK (quality IN ('standard', 'hd')),
    style VARCHAR(20) DEFAULT 'vivid' CHECK (style IN ('vivid', 'natural')),
    size VARCHAR(20) DEFAULT '1024x1024',
    token_count INTEGER,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 4. 生成图片表
CREATE TABLE generated_images (
    id BIGSERIAL PRIMARY KEY,
    topic_id BIGINT REFERENCES trending_topics(id) ON DELETE CASCADE,
    prompt_id BIGINT REFERENCES ai_prompts(id) ON DELETE SET NULL,
    original_url TEXT NOT NULL,
    local_path VARCHAR(500),
    file_name VARCHAR(255),
    file_format VARCHAR(10) DEFAULT 'PNG',
    file_size BIGINT,
    dimensions VARCHAR(20) DEFAULT '1024x1024',
    quality VARCHAR(20) DEFAULT 'standard',
    status VARCHAR(20) NOT NULL DEFAULT 'GENERATED' CHECK (status IN ('GENERATED', 'DOWNLOADING', 'DOWNLOADED', 'UPLOADED', 'FAILED')),
    generation_cost DECIMAL(8,4) DEFAULT 0.00,
    error_message TEXT,
    checksum VARCHAR(64), -- SHA-256 校验和
    s3_url TEXT,
    cdn_url TEXT,
    generated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    downloaded_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 5. 商品上架记录表
CREATE TABLE product_listings (
    id BIGSERIAL PRIMARY KEY,
    topic_id BIGINT REFERENCES trending_topics(id) ON DELETE CASCADE,
    image_id BIGINT REFERENCES generated_images(id) ON DELETE SET NULL,
    amazon_listing_id VARCHAR(100),
    asin VARCHAR(20),
    title VARCHAR(500) NOT NULL,
    description TEXT,
    keywords TEXT[], -- PostgreSQL数组类型
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    category VARCHAR(100),
    brand VARCHAR(100),
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'LISTING', 'LISTED', 'ACTIVE', 'INACTIVE', 'REMOVED', 'FAILED')),
    marketplace VARCHAR(50) DEFAULT 'Amazon',
    region VARCHAR(10) DEFAULT 'US',
    error_message TEXT,
    -- 销售数据
    view_count INTEGER DEFAULT 0,
    order_count INTEGER DEFAULT 0,
    total_revenue DECIMAL(12,2) DEFAULT 0.00,
    last_sale_at TIMESTAMP,
    listed_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 6. API使用日志表
CREATE TABLE api_usage_logs (
    id BIGSERIAL PRIMARY KEY,
    task_id BIGINT REFERENCES daily_tasks(id) ON DELETE SET NULL,
    api_provider VARCHAR(50) NOT NULL,
    api_endpoint VARCHAR(200) NOT NULL,
    request_method VARCHAR(10) NOT NULL,
    request_params JSONB, -- 使用JSONB存储请求参数
    response_status VARCHAR(20) NOT NULL CHECK (response_status IN ('SUCCESS', 'FAILED', 'TIMEOUT', 'RATE_LIMITED')),
    http_status_code INTEGER,
    response_message TEXT,
    response_time_ms BIGINT,
    cost DECIMAL(8,4) DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'USD',
    retry_count INTEGER DEFAULT 0,
    error_code VARCHAR(50),
    error_message TEXT,
    -- 配额信息
    daily_quota_used INTEGER,
    daily_quota_limit INTEGER,
    rate_limit_remaining INTEGER,
    rate_limit_reset_at TIMESTAMP,
    request_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    response_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 7. 系统配置表
CREATE TABLE system_configs (
    id BIGSERIAL PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT,
    config_type VARCHAR(20) DEFAULT 'STRING' CHECK (config_type IN ('STRING', 'INTEGER', 'DECIMAL', 'BOOLEAN', 'JSON')),
    description TEXT,
    is_sensitive BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 8. 成本统计表（每日汇总）
CREATE TABLE daily_cost_summary (
    id BIGSERIAL PRIMARY KEY,
    date DATE NOT NULL UNIQUE,
    openai_requests INTEGER DEFAULT 0,
    openai_cost DECIMAL(10,4) DEFAULT 0.00,
    twitter_requests INTEGER DEFAULT 0,
    twitter_cost DECIMAL(10,4) DEFAULT 0.00,
    amazon_requests INTEGER DEFAULT 0,
    amazon_cost DECIMAL(10,4) DEFAULT 0.00,
    total_requests INTEGER DEFAULT 0,
    total_cost DECIMAL(10,4) DEFAULT 0.00,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
-- 时间相关索引
CREATE INDEX idx_daily_tasks_execution_date ON daily_tasks(execution_date);
CREATE INDEX idx_daily_tasks_status ON daily_tasks(status);
CREATE INDEX idx_trending_topics_discovered_at ON trending_topics(discovered_at);
CREATE INDEX idx_trending_topics_task_id ON trending_topics(task_id);
CREATE INDEX idx_trending_topics_status ON trending_topics(status);
CREATE INDEX idx_trending_topics_topic_type ON trending_topics(topic_type);

-- 关联关系索引
CREATE INDEX idx_ai_prompts_topic_id ON ai_prompts(topic_id);
CREATE INDEX idx_ai_prompts_created_at ON ai_prompts(created_at);
CREATE INDEX idx_generated_images_topic_id ON generated_images(topic_id);
CREATE INDEX idx_generated_images_status ON generated_images(status);
CREATE INDEX idx_product_listings_topic_id ON product_listings(topic_id);
CREATE INDEX idx_product_listings_status ON product_listings(status);

-- API日志索引
CREATE INDEX idx_api_usage_logs_task_id ON api_usage_logs(task_id);
CREATE INDEX idx_api_usage_logs_provider ON api_usage_logs(api_provider);
CREATE INDEX idx_api_usage_logs_request_at ON api_usage_logs(request_at);

-- 文本搜索索引
CREATE INDEX idx_trending_topics_topic_gin ON trending_topics USING gin(topic gin_trgm_ops);
CREATE INDEX idx_ai_prompts_text_gin ON ai_prompts USING gin(prompt_text gin_trgm_ops);

-- 成本统计索引
CREATE INDEX idx_daily_cost_summary_date ON daily_cost_summary(date);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表添加更新时间触发器
CREATE TRIGGER update_daily_tasks_updated_at BEFORE UPDATE ON daily_tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_product_listings_updated_at BEFORE UPDATE ON product_listings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_configs_updated_at BEFORE UPDATE ON system_configs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_daily_cost_summary_updated_at BEFORE UPDATE ON daily_cost_summary
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入默认系统配置
INSERT INTO system_configs (config_key, config_value, config_type, description) VALUES
('system.version', '2.0.0', 'STRING', '系统版本号'),
('openai.max_daily_requests', '50', 'INTEGER', 'OpenAI每日最大请求数'),
('openai.max_daily_budget', '10.00', 'DECIMAL', 'OpenAI每日最大预算(USD)'),
('twitter.max_daily_requests', '1000', 'INTEGER', 'Twitter每日最大请求数'),
('amazon.max_daily_requests', '100', 'INTEGER', 'Amazon每日最大请求数'),
('processing.max_trends_per_run', '5', 'INTEGER', '每次运行最大处理热搜数'),
('processing.delay_between_requests', '3000', 'INTEGER', '请求间隔时间(毫秒)'),
('image.default_quality', 'standard', 'STRING', '默认图片质量'),
('image.default_size', '1024x1024', 'STRING', '默认图片尺寸'),
('scheduler.daily_job_time', '09:00', 'STRING', '每日任务执行时间'),
('data.retention_days', '90', 'INTEGER', '数据保留天数');

-- 创建视图以简化常用查询
-- 任务执行概览视图
CREATE VIEW v_task_overview AS
SELECT 
    dt.id,
    dt.execution_date,
    dt.status,
    dt.total_trends,
    dt.processed_trends,
    dt.successful_images,
    dt.successful_listings,
    dt.total_cost,
    dt.execution_time_ms,
    COUNT(tt.id) as actual_topics_count,
    COUNT(gi.id) as actual_images_count,
    COUNT(pl.id) as actual_listings_count
FROM daily_tasks dt
LEFT JOIN trending_topics tt ON dt.id = tt.task_id
LEFT JOIN generated_images gi ON tt.id = gi.topic_id
LEFT JOIN product_listings pl ON tt.id = pl.topic_id
GROUP BY dt.id, dt.execution_date, dt.status, dt.total_trends, dt.processed_trends, 
         dt.successful_images, dt.successful_listings, dt.total_cost, dt.execution_time_ms;

-- 热搜话题详情视图
CREATE VIEW v_topic_details AS
SELECT 
    tt.id,
    tt.task_id,
    tt.topic,
    tt.topic_type,
    tt.ranking,
    tt.source,
    tt.language,
    tt.status,
    tt.discovered_at,
    ap.id as prompt_id,
    ap.prompt_text,
    ap.model as ai_model,
    ap.quality as ai_quality,
    gi.id as image_id,
    gi.file_name,
    gi.generation_cost,
    gi.status as image_status,
    pl.id as listing_id,
    pl.amazon_listing_id,
    pl.asin,
    pl.title as product_title,
    pl.price,
    pl.status as listing_status
FROM trending_topics tt
LEFT JOIN ai_prompts ap ON tt.id = ap.topic_id
LEFT JOIN generated_images gi ON tt.id = gi.topic_id
LEFT JOIN product_listings pl ON tt.id = pl.topic_id;

-- 成本分析视图
CREATE VIEW v_cost_analysis AS
SELECT 
    DATE(aul.request_at) as date,
    aul.api_provider,
    COUNT(*) as request_count,
    SUM(aul.cost) as total_cost,
    AVG(aul.response_time_ms) as avg_response_time,
    COUNT(CASE WHEN aul.response_status = 'SUCCESS' THEN 1 END) as success_count,
    COUNT(CASE WHEN aul.response_status = 'FAILED' THEN 1 END) as failure_count
FROM api_usage_logs aul
GROUP BY DATE(aul.request_at), aul.api_provider
ORDER BY date DESC, total_cost DESC;

COMMENT ON TABLE daily_tasks IS '每日任务执行记录表';
COMMENT ON TABLE trending_topics IS '热搜话题表';
COMMENT ON TABLE ai_prompts IS 'AI提示词表';
COMMENT ON TABLE generated_images IS '生成图片表';
COMMENT ON TABLE product_listings IS '商品上架记录表';
COMMENT ON TABLE api_usage_logs IS 'API使用日志表';
COMMENT ON TABLE system_configs IS '系统配置表';
COMMENT ON TABLE daily_cost_summary IS '每日成本汇总表';
