<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hal.mapper.TrendingTopicMapper">

    <!-- 结果映射 -->
    <resultMap id="trendingTopicResultMap" type="TrendingTopic">
        <id property="id" column="id"/>
        <result property="taskId" column="task_id"/>
        <result property="topic" column="topic"/>
        <result property="topicType" column="topic_type"/>
        <result property="ranking" column="ranking"/>
        <result property="source" column="source"/>
        <result property="language" column="language"/>
        <result property="status" column="status"/>
        <result property="discoveredAt" column="discovered_at"/>
        <result property="createdAt" column="created_at"/>
    </resultMap>

    <!-- 插入话题 -->
    <insert id="insert" parameterType="TrendingTopic" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO trending_topics (
            task_id, topic, topic_type, ranking, source,
            language, status, discovered_at, created_at
        ) VALUES (
            #{taskId}, #{topic}, #{topicType}, #{ranking}, #{source},
            #{language}, #{status}, #{discoveredAt}, #{createdAt}
        )
    </insert>

    <!-- 更新话题状态 -->
    <update id="updateStatus">
        UPDATE trending_topics SET status = #{status} WHERE id = #{id}
    </update>

    <!-- 更新话题类型 -->
    <update id="updateTopicType">
        UPDATE trending_topics SET topic_type = #{topicType} WHERE id = #{id}
    </update>

    <!-- 根据ID查询 -->
    <select id="findById" parameterType="long" resultMap="trendingTopicResultMap">
        SELECT * FROM trending_topics WHERE id = #{id}
    </select>

    <!-- 根据任务ID查询 -->
    <select id="findByTaskId" parameterType="long" resultMap="trendingTopicResultMap">
        SELECT * FROM trending_topics
        WHERE task_id = #{taskId}
        ORDER BY ranking ASC
    </select>

    <!-- 根据日期查询 -->
    <select id="findByDate" parameterType="java.time.LocalDateTime" resultMap="trendingTopicResultMap">
        SELECT * FROM trending_topics
        WHERE DATE(discovered_at) = DATE(#{date})
        ORDER BY ranking ASC
    </select>

    <!-- 根据话题类型查询 -->
    <select id="findByTopicType" resultMap="trendingTopicResultMap">
        SELECT * FROM trending_topics
        WHERE topic_type = #{topicType}
        ORDER BY discovered_at DESC
        LIMIT #{limit}
    </select>

    <!-- 根据状态查询 -->
    <select id="findByStatus" parameterType="string" resultMap="trendingTopicResultMap">
        SELECT * FROM trending_topics
        WHERE status = #{status}
        ORDER BY discovered_at DESC
    </select>

    <!-- 搜索话题 -->
    <select id="searchByKeyword" resultMap="trendingTopicResultMap">
        SELECT * FROM trending_topics
        WHERE topic ILIKE CONCAT('%', #{keyword}, '%')
        ORDER BY discovered_at DESC
        LIMIT #{limit}
    </select>

    <!-- 查询热门话题 -->
    <select id="findPopularTopics" resultType="com.hal.entity.TopicFrequency">
        SELECT
            topic,
            COUNT(*) as frequency,
            MAX(discovered_at) as lastSeen
        FROM trending_topics
        WHERE discovered_at >= #{startDate}
        GROUP BY topic
        HAVING COUNT(*) >= #{minFrequency}
        ORDER BY frequency DESC, lastSeen DESC
        LIMIT #{limit}
    </select>

    <!-- 统计话题类型分布 -->
    <select id="getTopicTypeStatistics" resultType="com.hal.entity.TopicTypeStats">
        SELECT
            topic_type as topicType,
            COUNT(*) as count,
            COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completedCount
        FROM trending_topics
        WHERE discovered_at >= #{startDate} AND discovered_at &lt;= #{endDate}
        GROUP BY topic_type
        ORDER BY count DESC
    </select>

    <!-- 检查话题是否已存在 -->
    <select id="countByTopicAndDate" resultType="int">
        SELECT COUNT(*) FROM trending_topics
        WHERE topic = #{topic} AND DATE(discovered_at) = DATE(#{date})
    </select>

    <!-- 删除指定ID的话题 -->
    <delete id="deleteById" parameterType="long">
        DELETE FROM trending_topics WHERE id = #{id}
    </delete>

    <!-- 删除指定日期之前的话题 -->
    <delete id="deleteBeforeDate" parameterType="java.time.LocalDateTime">
        DELETE FROM trending_topics WHERE discovered_at &lt; #{date}
    </delete>

    <!-- 分页查询话题 -->
    <select id="findTopicsWithPagination" resultMap="trendingTopicResultMap">
        SELECT * FROM trending_topics
        <where>
            <if test="taskId != null">
                AND task_id = #{taskId}
            </if>
            <if test="topicType != null and topicType != ''">
                AND topic_type = #{topicType}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="source != null and source != ''">
                AND source = #{source}
            </if>
            <if test="language != null and language != ''">
                AND language = #{language}
            </if>
            <if test="keyword != null and keyword != ''">
                AND topic ILIKE CONCAT('%', #{keyword}, '%')
            </if>
            <if test="startDate != null">
                AND discovered_at >= #{startDate}
            </if>
            <if test="endDate != null">
                AND discovered_at &lt;= #{endDate}
            </if>
        </where>
        ORDER BY discovered_at DESC, ranking ASC
    </select>

    <!-- 获取话题处理进度 -->
    <select id="getTopicProgress" resultType="map">
        SELECT
            status,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
        FROM trending_topics
        <where>
            <if test="taskId != null">
                AND task_id = #{taskId}
            </if>
            <if test="startDate != null">
                AND discovered_at >= #{startDate}
            </if>
        </where>
        GROUP BY status
        ORDER BY
            CASE status
                WHEN 'COMPLETED' THEN 1
                WHEN 'PROCESSING' THEN 2
                WHEN 'PENDING' THEN 3
                WHEN 'FAILED' THEN 4
                ELSE 5
            END
    </select>

    <!-- 获取话题趋势分析 -->
    <select id="getTopicTrends" resultType="map">
        SELECT
            DATE(discovered_at) as date,
            topic_type,
            COUNT(*) as count,
            COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed_count
        FROM trending_topics
        WHERE discovered_at >= #{startDate} AND discovered_at &lt;= #{endDate}
        GROUP BY DATE(discovered_at), topic_type
        ORDER BY date DESC, topic_type
    </select>

    <!-- 获取热搜排行榜 -->
    <select id="getTrendingRanking" resultType="map">
        SELECT
            topic,
            topic_type,
            MIN(ranking) as best_ranking,
            COUNT(*) as appearance_count,
            MAX(discovered_at) as last_seen,
            STRING_AGG(DISTINCT source, ', ') as sources
        FROM trending_topics
        WHERE discovered_at >= #{startDate}
        GROUP BY topic, topic_type
        ORDER BY appearance_count DESC, best_ranking ASC
        LIMIT #{limit}
    </select>

</mapper>
