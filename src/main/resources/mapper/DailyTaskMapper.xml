<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hal.mapper.DailyTaskMapper">

    <!-- 结果映射 -->
    <resultMap id="dailyTaskResultMap" type="DailyTask">
        <id property="id" column="id"/>
        <result property="executionDate" column="execution_date"/>
        <result property="status" column="status"/>
        <result property="totalTrends" column="total_trends"/>
        <result property="processedTrends" column="processed_trends"/>
        <result property="successfulImages" column="successful_images"/>
        <result property="successfulListings" column="successful_listings"/>
        <result property="totalCost" column="total_cost"/>
        <result property="errorMessage" column="error_message"/>
        <result property="executionTimeMs" column="execution_time_ms"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <!-- 插入任务 -->
    <insert id="insert" parameterType="DailyTask" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO daily_tasks (
            execution_date, status, total_trends, processed_trends,
            successful_images, successful_listings, total_cost,
            error_message, execution_time_ms, created_at, updated_at
        ) VALUES (
            #{executionDate}, #{status}, #{totalTrends}, #{processedTrends},
            #{successfulImages}, #{successfulListings}, #{totalCost},
            #{errorMessage}, #{executionTimeMs}, #{createdAt}, #{updatedAt}
        )
    </insert>

    <!-- 更新任务 -->
    <update id="update" parameterType="DailyTask">
        UPDATE daily_tasks SET
            status = #{status},
            total_trends = #{totalTrends},
            processed_trends = #{processedTrends},
            successful_images = #{successfulImages},
            successful_listings = #{successfulListings},
            total_cost = #{totalCost},
            error_message = #{errorMessage},
            execution_time_ms = #{executionTimeMs},
            updated_at = #{updatedAt}
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询 -->
    <select id="findById" parameterType="long" resultMap="dailyTaskResultMap">
        SELECT * FROM daily_tasks WHERE id = #{id}
    </select>

    <!-- 根据日期查询 -->
    <select id="findByDate" parameterType="java.time.LocalDate" resultMap="dailyTaskResultMap">
        SELECT * FROM daily_tasks
        WHERE DATE(execution_date) = #{date}
        ORDER BY execution_date DESC
    </select>

    <!-- 查询最近的任务 -->
    <select id="findRecent" parameterType="int" resultMap="dailyTaskResultMap">
        SELECT * FROM daily_tasks
        ORDER BY execution_date DESC
        LIMIT #{limit}
    </select>

    <!-- 根据状态查询 -->
    <select id="findByStatus" parameterType="string" resultMap="dailyTaskResultMap">
        SELECT * FROM daily_tasks
        WHERE status = #{status}
        ORDER BY execution_date DESC
    </select>

    <!-- 根据日期范围查询 -->
    <select id="findByDateRange" resultMap="dailyTaskResultMap">
        SELECT * FROM daily_tasks
        WHERE execution_date >= #{startDate} AND execution_date &lt;= #{endDate}
        ORDER BY execution_date DESC
    </select>

    <!-- 获取统计信息 -->
    <select id="getStatistics" resultType="com.hal.entity.TaskStatistics">
        SELECT
            COUNT(*) as totalTasks,
            SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as successfulTasks,
            SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failedTasks,
            SUM(CASE WHEN status = 'PARTIAL' THEN 1 ELSE 0 END) as partialTasks,
            SUM(COALESCE(total_cost, 0)) as totalCost,
            AVG(COALESCE(execution_time_ms, 0)) as avgExecutionTime
        FROM daily_tasks
        WHERE execution_date >= #{startDate} AND execution_date &lt;= #{endDate}
    </select>

    <!-- 删除指定ID的任务 -->
    <delete id="deleteById" parameterType="long">
        DELETE FROM daily_tasks WHERE id = #{id}
    </delete>

    <!-- 删除指定日期之前的任务 -->
    <delete id="deleteBeforeDate" parameterType="java.time.LocalDateTime">
        DELETE FROM daily_tasks WHERE execution_date &lt; #{date}
    </delete>

    <!-- 分页查询任务 -->
    <select id="findTasksWithPagination" resultMap="dailyTaskResultMap">
        SELECT * FROM daily_tasks
        <where>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="startDate != null">
                AND execution_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AND execution_date &lt;= #{endDate}
            </if>
        </where>
        ORDER BY execution_date DESC
    </select>

    <!-- 获取任务概览统计 -->
    <select id="getTaskOverview" resultType="map">
        SELECT
            COUNT(*) as total_count,
            COUNT(CASE WHEN status = 'SUCCESS' THEN 1 END) as success_count,
            COUNT(CASE WHEN status = 'FAILED' THEN 1 END) as failed_count,
            COUNT(CASE WHEN status = 'PARTIAL' THEN 1 END) as partial_count,
            COUNT(CASE WHEN status = 'RUNNING' THEN 1 END) as running_count,
            SUM(COALESCE(total_cost, 0)) as total_cost,
            AVG(COALESCE(execution_time_ms, 0)) as avg_execution_time,
            MAX(execution_date) as last_execution
        FROM daily_tasks
        WHERE execution_date >= #{startDate}
    </select>

    <!-- 获取每日统计 -->
    <select id="getDailyStatistics" resultType="map">
        SELECT
            DATE(execution_date) as date,
            COUNT(*) as task_count,
            COUNT(CASE WHEN status = 'SUCCESS' THEN 1 END) as success_count,
            SUM(COALESCE(total_trends, 0)) as total_trends,
            SUM(COALESCE(processed_trends, 0)) as processed_trends,
            SUM(COALESCE(successful_images, 0)) as successful_images,
            SUM(COALESCE(successful_listings, 0)) as successful_listings,
            SUM(COALESCE(total_cost, 0)) as total_cost
        FROM daily_tasks
        WHERE execution_date >= #{startDate} AND execution_date &lt;= #{endDate}
        GROUP BY DATE(execution_date)
        ORDER BY date DESC
    </select>

</mapper>
