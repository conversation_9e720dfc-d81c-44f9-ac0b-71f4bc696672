<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">

<configuration>
    <!-- 设置 -->
    <settings>
        <!-- 开启驼峰命名转换 -->
        <setting name="mapUnderscoreToCamelCase" value="true"/>
        <!-- 开启自动生成主键 -->
        <setting name="useGeneratedKeys" value="true"/>
        <!-- 设置默认执行器类型 -->
        <setting name="defaultExecutorType" value="REUSE"/>
        <!-- 设置超时时间 -->
        <setting name="defaultStatementTimeout" value="30"/>
        <!-- 开启延迟加载 -->
        <setting name="lazyLoadingEnabled" value="true"/>
        <!-- 设置积极延迟加载 -->
        <setting name="aggressiveLazyLoading" value="false"/>
        <!-- 开启二级缓存 -->
        <setting name="cacheEnabled" value="true"/>
        <!-- 开启本地缓存 -->
        <setting name="localCacheScope" value="SESSION"/>
        <!-- 设置JDBC类型为null时的处理 -->
        <setting name="jdbcTypeForNull" value="NULL"/>
        <!-- 设置日志实现 -->
        <setting name="logImpl" value="STDOUT_LOGGING"/>
    </settings>

    <!-- 类型别名 -->
    <typeAliases>
        <typeAlias alias="DailyTask" type="com.hal.entity.DailyTask"/>
        <typeAlias alias="TrendingTopic" type="com.hal.entity.TrendingTopic"/>
        <typeAlias alias="AiPrompt" type="com.hal.entity.AiPrompt"/>
        <typeAlias alias="GeneratedImage" type="com.hal.entity.GeneratedImage"/>
        <typeAlias alias="ProductListing" type="com.hal.entity.ProductListing"/>
        <typeAlias alias="ApiUsageLog" type="com.hal.entity.ApiUsageLog"/>
    </typeAliases>

    <!-- 类型处理器 -->
    <typeHandlers>
        <!-- PostgreSQL数组类型处理器 -->
        <typeHandler handler="com.hal.config.ArrayTypeHandler" javaType="java.util.List"/>
        <!-- JSON类型处理器 -->
        <typeHandler handler="com.hal.config.JsonTypeHandler" javaType="java.lang.Object"/>
    </typeHandlers>

    <!-- 插件 -->
    <plugins>
        <!-- 分页插件 -->
        <plugin interceptor="com.github.pagehelper.PageInterceptor">
            <property name="helperDialect" value="postgresql"/>
            <property name="reasonable" value="true"/>
            <property name="supportMethodsArguments" value="true"/>
            <property name="params" value="count=countSql"/>
        </plugin>

        <!-- 性能监控插件 -->
        <plugin interceptor="com.hal.config.PerformanceInterceptor">
            <property name="maxTime" value="1000"/>
            <property name="format" value="true"/>
        </plugin>
    </plugins>

    <!-- 环境配置 -->
    <environments default="development">
        <environment id="development">
            <transactionManager type="JDBC"/>
            <dataSource type="POOLED">
                <property name="driver" value="org.postgresql.Driver"/>
                <property name="url" value="*********************************************"/>
                <property name="username" value="postgres"/>
                <property name="password" value="password"/>
                <!-- 连接池配置 -->
                <property name="poolMaximumActiveConnections" value="10"/>
                <property name="poolMaximumIdleConnections" value="5"/>
                <property name="poolMaximumCheckoutTime" value="20000"/>
                <property name="poolTimeToWait" value="20000"/>
                <property name="poolPingEnabled" value="true"/>
                <property name="poolPingQuery" value="SELECT 1"/>
                <property name="poolPingConnectionsNotUsedFor" value="19000"/>
            </dataSource>
        </environment>

        <environment id="production">
            <transactionManager type="JDBC"/>
            <dataSource type="JNDI">
                <property name="data_source" value="java:comp/env/jdbc/TrendTShirtDB"/>
            </dataSource>
        </environment>
    </environments>

    <!-- 映射器 -->
    <mappers>
        <mapper class="com.hal.mapper.DailyTaskMapper"/>
        <mapper class="com.hal.mapper.TrendingTopicMapper"/>
        <mapper class="com.hal.mapper.AiPromptMapper"/>
        <mapper class="com.hal.mapper.GeneratedImageMapper"/>
        <mapper class="com.hal.mapper.ProductListingMapper"/>
        <mapper class="com.hal.mapper.ApiUsageLogMapper"/>
    </mappers>
</configuration>
