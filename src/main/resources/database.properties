# ??T?????? - ???????

# PostgreSQL???????
database.url=*********************************************
database.username=postgres
database.password=password

# ?????
database.pool.max-size=10
database.pool.min-idle=2
database.pool.connection-timeout=30000
database.pool.idle-timeout=600000
database.pool.max-lifetime=1800000

# ???????
database.show-sql=false
database.format-sql=true
database.use-unicode=true
database.character-encoding=utf8

# ????
database.migration.enabled=true
database.migration.baseline-on-migrate=true
database.migration.validate-on-migrate=true

# ????
database.cache.enabled=true
database.cache.size=1000
database.lazy-loading=true

# ??????
database.dev.show-sql=true
database.dev.format-sql=true
database.dev.log-slow-queries=true
database.dev.slow-query-threshold=1000

# ??????
database.prod.show-sql=false
database.prod.format-sql=false
database.prod.log-slow-queries=true
database.prod.slow-query-threshold=500
