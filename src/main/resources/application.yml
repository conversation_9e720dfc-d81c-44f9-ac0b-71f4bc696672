# Spring Boot 应用配置
server:
  port: 8080
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: trend-tshirt-service
  
  # 数据源配置
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: org.postgresql.Driver
    url: *********************************************
    username: postgres
    password: password
    hikari:
      pool-name: TrendTShirtHikariCP
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-test-query: SELECT 1
      validation-timeout: 5000
      leak-detection-threshold: 60000
  
  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms
  
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  
  # Flyway数据库迁移
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true
    encoding: UTF-8
  
  # Quartz定时任务
  quartz:
    job-store-type: jdbc
    jdbc:
      initialize-schema: embedded
    properties:
      org:
        quartz:
          scheduler:
            instanceName: TrendTShirtScheduler
            instanceId: AUTO
          jobStore:
            class: org.quartz.impl.jdbcjobstore.JobStoreTX
            driverDelegateClass: org.quartz.impl.jdbcjobstore.PostgreSQLDelegate
            useProperties: false
            tablePrefix: QRTZ_
            isClustered: false
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5

# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.hal.entity
  configuration:
    map-underscore-to-camel-case: true
    use-generated-keys: true
    default-executor-type: reuse
    default-statement-timeout: 30
    lazy-loading-enabled: true
    aggressive-lazy-loading: false
    cache-enabled: true
    local-cache-scope: session
    jdbc-type-for-null: null
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 分页插件配置
pagehelper:
  helper-dialect: postgresql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,flyway,quartz
      base-path: /actuator
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# Swagger API文档配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  packages-to-scan: com.hal.controller

# 日志配置
logging:
  level:
    com.hal: DEBUG
    org.springframework: INFO
    org.mybatis: DEBUG
    com.zaxxer.hikari: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/trend-tshirt.log
    max-size: 100MB
    max-history: 30

# 自定义配置
app:
  # API配置
  api:
    openai:
      base-url: https://api.openai.com
      max-daily-requests: 50
      max-daily-budget: 10.00
      standard-cost: 0.04
      hd-cost: 0.08
    twitter:
      base-url: https://api.twitter.com
      max-daily-requests: 1000
      rate-limit-delay: 15000
    amazon:
      max-daily-requests: 100
      default-brand: TrendTee
  
  # 处理配置
  processing:
    max-trends-per-run: 5
    delay-between-requests: 3000
    max-retries: 3
  
  # 文件配置
  file:
    image-directory: images
    upload-path: /uploads
    max-file-size: 10MB
  
  # 调度配置
  scheduler:
    daily-job-time: "09:00"
    timezone: Asia/Shanghai
    enabled: true
  
  # 数据保留配置
  data:
    retention-days: 90
    log-retention-days: 30
    failed-image-retention-days: 7
  
  # 代理配置
  proxy:
    enabled: true
    host: 127.0.0.1
    port: 7890
