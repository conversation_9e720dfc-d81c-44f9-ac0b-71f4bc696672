# Quartz???????

# ???????
org.quartz.scheduler.instanceName = TrendTShirtScheduler
org.quartz.scheduler.instanceId = AUTO

# ?????
org.quartz.threadPool.class = org.quartz.simpl.SimpleThreadPool
org.quartz.threadPool.threadCount = 3
org.quartz.threadPool.threadPriority = 5

# ??????????????
org.quartz.jobStore.class = org.quartz.simpl.RAMJobStore

# ????
org.quartz.plugin.triggHistory.class = org.quartz.plugins.history.LoggingTriggerHistoryPlugin
org.quartz.plugin.triggHistory.triggerFiredMessage = Trigger {1}.{0} fired job {6}.{5} at: {4, date, HH:mm:ss MM/dd/yyyy}
org.quartz.plugin.triggHistory.triggerCompleteMessage = Trigger {1}.{0} completed firing job {6}.{5} at {4, date, HH:mm:ss MM/dd/yyyy} with result: {9}

# ????
org.quartz.scheduler.skipUpdateCheck = true
