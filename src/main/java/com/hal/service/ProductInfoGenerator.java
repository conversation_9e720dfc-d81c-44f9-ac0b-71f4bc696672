package com.hal.service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * 商品信息生成器
 * 根据热门话题自动生成T恤商品的标题、描述、关键词等信息
 */
public class ProductInfoGenerator {

    private static final Random random = new Random();

    // T恤基础描述模板
    private static final List<String> SHIRT_DESCRIPTIONS = Arrays.asList(
        "Premium quality cotton t-shirt with unique satirical design",
        "Comfortable and stylish tee featuring witty commentary on current events",
        "High-quality graphic t-shirt with humorous take on trending topics",
        "Soft cotton blend shirt with eye-catching cartoon illustration",
        "Trendy t-shirt design that makes a statement about today's world"
    );

    // 价格范围
    private static final double MIN_PRICE = 19.99;
    private static final double MAX_PRICE = 29.99;

    /**
     * 根据话题生成完整的商品信息
     */
    public AmazonListingService.ProductInfo generateProductInfo(String topic, String imageUrl, String localImagePath) {
        String title = generateTitle(topic);
        String description = generateDescription(topic);
        List<String> keywords = generateKeywords(topic);
        BigDecimal price = generatePrice();

        AmazonListingService.ProductInfo product = new AmazonListingService.ProductInfo();
        product.setTopic(topic);
        product.setTitle(title);
        product.setDescription(description);
        product.setKeywords(keywords);
        product.setPrice(price);
        product.setImageUrl(imageUrl);
        product.setLocalImagePath(localImagePath);

        return product;
    }

    /**
     * 生成商品标题
     */
    private String generateTitle(String topic) {
        String topicType = detectTopicType(topic);

        return switch (topicType) {
            case "technology" -> generateTechTitle(topic);
            case "environment" -> generateEnvironmentTitle(topic);
            case "politics" -> generatePoliticsTitle(topic);
            case "finance" -> generateFinanceTitle(topic);
            case "entertainment" -> generateEntertainmentTitle(topic);
            case "sports" -> generateSportsTitle(topic);
            default -> generateGeneralTitle(topic);
        };
    }

    /**
     * 生成科技类标题
     */
    private String generateTechTitle(String topic) {
        List<String> templates = Arrays.asList(
            "AI Revolution: %s Satirical T-Shirt Design",
            "Tech Humor: %s Funny Cartoon Tee",
            "Digital Age Satire: %s Graphic T-Shirt",
            "Future Shock: %s Witty Tech Shirt",
            "Silicon Valley Humor: %s Satirical Design"
        );
        return String.format(templates.get(random.nextInt(templates.size())), topic);
    }

    /**
     * 生成环境类标题
     */
    private String generateEnvironmentTitle(String topic) {
        List<String> templates = Arrays.asList(
            "Climate Reality: %s Environmental Satire Tee",
            "Green Humor: %s Eco-Friendly Cartoon Shirt",
            "Planet Earth: %s Environmental Awareness T-Shirt",
            "Save the Planet: %s Satirical Design",
            "Eco Warrior: %s Environmental Humor Tee"
        );
        return String.format(templates.get(random.nextInt(templates.size())), topic);
    }

    /**
     * 生成政治类标题
     */
    private String generatePoliticsTitle(String topic) {
        List<String> templates = Arrays.asList(
            "Political Satire: %s Commentary T-Shirt",
            "Democracy Humor: %s Political Cartoon Tee",
            "Civic Engagement: %s Satirical Design",
            "Political Commentary: %s Witty Shirt",
            "Vote Smart: %s Political Humor Tee"
        );
        return String.format(templates.get(random.nextInt(templates.size())), topic);
    }

    /**
     * 生成金融类标题
     */
    private String generateFinanceTitle(String topic) {
        List<String> templates = Arrays.asList(
            "Market Madness: %s Financial Satire Tee",
            "Money Talks: %s Economic Humor Shirt",
            "Wall Street Wisdom: %s Financial Comedy T-Shirt",
            "Crypto Chaos: %s Investment Humor Tee",
            "Economic Reality: %s Financial Satirical Design"
        );
        return String.format(templates.get(random.nextInt(templates.size())), topic);
    }

    /**
     * 生成娱乐类标题
     */
    private String generateEntertainmentTitle(String topic) {
        List<String> templates = Arrays.asList(
            "Pop Culture: %s Entertainment Satire Tee",
            "Celebrity Life: %s Hollywood Humor Shirt",
            "Fame Game: %s Entertainment Industry T-Shirt",
            "Showbiz Satire: %s Celebrity Commentary Tee",
            "Red Carpet Reality: %s Entertainment Humor Shirt"
        );
        return String.format(templates.get(random.nextInt(templates.size())), topic);
    }

    /**
     * 生成体育类标题
     */
    private String generateSportsTitle(String topic) {
        List<String> templates = Arrays.asList(
            "Sports Fanatic: %s Athletic Humor Tee",
            "Game Day: %s Sports Satire Shirt",
            "Athletic Life: %s Sports Commentary T-Shirt",
            "Team Spirit: %s Sports Humor Tee",
            "Championship Dreams: %s Athletic Satirical Design"
        );
        return String.format(templates.get(random.nextInt(templates.size())), topic);
    }

    /**
     * 生成通用标题
     */
    private String generateGeneralTitle(String topic) {
        List<String> templates = Arrays.asList(
            "Trending Now: %s Satirical T-Shirt Design",
            "Current Events: %s Humorous Cartoon Tee",
            "Social Commentary: %s Witty Graphic Shirt",
            "Modern Life: %s Satirical Design T-Shirt",
            "Today's World: %s Humorous Commentary Tee"
        );
        return String.format(templates.get(random.nextInt(templates.size())), topic);
    }

    /**
     * 生成商品描述
     */
    private String generateDescription(String topic) {
        String baseDescription = SHIRT_DESCRIPTIONS.get(random.nextInt(SHIRT_DESCRIPTIONS.size()));
        String topicSpecific = generateTopicSpecificDescription(topic);
        String features = generateFeatures();

        return String.format(
            "%s about '%s'. %s\n\n%s\n\nPerfect for anyone who appreciates clever humor and current events commentary.",
            baseDescription, topic, topicSpecific, features
        );
    }

    /**
     * 生成话题特定描述
     */
    private String generateTopicSpecificDescription(String topic) {
        String topicType = detectTopicType(topic);

        return switch (topicType) {
            case "technology" -> "This design cleverly captures the irony of our digital age and our complex relationship with technology.";
            case "environment" -> "A thought-provoking design that highlights environmental issues with a touch of dark humor.";
            case "politics" -> "Features political satire that's both funny and insightful, perfect for civic-minded individuals.";
            case "finance" -> "Captures the wild world of finance and economics with relatable humor about money matters.";
            case "entertainment" -> "A witty take on celebrity culture and the entertainment industry's quirks.";
            case "sports" -> "Celebrates sports culture with humor that any fan can appreciate.";
            default -> "A clever satirical take on current events that will spark conversations.";
        };
    }

    /**
     * 生成产品特性描述
     */
    private String generateFeatures() {
        return """
            Features:
            • 100% premium cotton for maximum comfort
            • High-quality digital printing that won't fade
            • Available in multiple sizes (S-XXL)
            • Unisex design suitable for everyone
            • Machine washable and durable
            • Unique satirical artwork you won't find anywhere else
            """;
    }

    /**
     * 生成关键词
     */
    private List<String> generateKeywords(String topic) {
        List<String> baseKeywords = Arrays.asList(
            "satirical t-shirt", "funny tee", "cartoon design", "graphic shirt",
            "humorous clothing", "witty apparel", "current events", "social commentary",
            "trendy shirt", "unique design", "conversation starter", "gift idea"
        );

        List<String> topicKeywords = generateTopicKeywords(topic);

        // 合并基础关键词和话题特定关键词
        List<String> allKeywords = new java.util.ArrayList<>(baseKeywords);
        allKeywords.addAll(topicKeywords);

        return allKeywords;
    }

    /**
     * 生成话题特定关键词
     */
    private List<String> generateTopicKeywords(String topic) {
        String topicType = detectTopicType(topic);

        return switch (topicType) {
            case "technology" -> Arrays.asList("tech humor", "AI shirt", "robot design", "digital age", "tech satire", "geek apparel");
            case "environment" -> Arrays.asList("climate shirt", "eco humor", "environmental awareness", "green design", "planet earth", "sustainability");
            case "politics" -> Arrays.asList("political satire", "democracy shirt", "civic humor", "election design", "political commentary", "vote shirt");
            case "finance" -> Arrays.asList("money humor", "financial satire", "crypto shirt", "investment design", "economic commentary", "wall street");
            case "entertainment" -> Arrays.asList("celebrity humor", "hollywood satire", "pop culture", "entertainment design", "fame commentary", "showbiz");
            case "sports" -> Arrays.asList("sports humor", "athletic design", "fan shirt", "game day", "sports satire", "team spirit");
            default -> Arrays.asList("trending topic", "current events", "social humor", "modern life", "contemporary design", "viral topic");
        };
    }

    /**
     * 生成价格
     */
    private BigDecimal generatePrice() {
        // 在价格范围内生成随机价格，保留两位小数
        double price = MIN_PRICE + (MAX_PRICE - MIN_PRICE) * random.nextDouble();
        return BigDecimal.valueOf(Math.round(price * 100.0) / 100.0);
    }

    /**
     * 检测话题类型
     */
    private String detectTopicType(String topic) {
        String lowerTopic = topic.toLowerCase();

        if (lowerTopic.contains("ai") || lowerTopic.contains("人工智能") || lowerTopic.contains("chatgpt") ||
            lowerTopic.contains("robot") || lowerTopic.contains("tech") || lowerTopic.contains("digital")) {
            return "technology";
        } else if (lowerTopic.contains("climate") || lowerTopic.contains("环境") || lowerTopic.contains("global warming") ||
                   lowerTopic.contains("气候") || lowerTopic.contains("environment") || lowerTopic.contains("green")) {
            return "environment";
        } else if (lowerTopic.contains("politics") || lowerTopic.contains("政治") || lowerTopic.contains("election") ||
                   lowerTopic.contains("government") || lowerTopic.contains("democracy") || lowerTopic.contains("vote")) {
            return "politics";
        } else if (lowerTopic.contains("crypto") || lowerTopic.contains("bitcoin") || lowerTopic.contains("加密货币") ||
                   lowerTopic.contains("股市") || lowerTopic.contains("finance") || lowerTopic.contains("money")) {
            return "finance";
        } else if (lowerTopic.contains("celebrity") || lowerTopic.contains("明星") || lowerTopic.contains("entertainment") ||
                   lowerTopic.contains("娱乐") || lowerTopic.contains("hollywood") || lowerTopic.contains("movie")) {
            return "entertainment";
        } else if (lowerTopic.contains("sports") || lowerTopic.contains("体育") || lowerTopic.contains("football") ||
                   lowerTopic.contains("basketball") || lowerTopic.contains("game") || lowerTopic.contains("team")) {
            return "sports";
        } else {
            return "general";
        }
    }
}
