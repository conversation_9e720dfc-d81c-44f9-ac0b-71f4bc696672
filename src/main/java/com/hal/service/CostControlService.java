package com.hal.service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import com.google.genai.Client;
import com.google.genai.types.GenerateContentResponse;
/**
 * 成本控制服务
 * 管理API调用次数、成本预算和使用配额
 */
public class CostControlService {

    // 配额文件路径
    private static final String QUOTA_FILE = "quota_usage.txt";

    // API成本配置（美元）
    private static final double OPENAI_DALLE3_COST_PER_IMAGE = 0.04; // DALL-E 3 标准质量
    private static final double OPENAI_DALLE3_HD_COST_PER_IMAGE = 0.08; // DALL-E 3 高清质量
    private static final double GEMINI_COST_PER_IMAGE = 0.0025; // Gemini成本更低
    private static final double TWITTER_API_COST_PER_REQUEST = 0.0; // Twitter API v2 基础版免费
    private static final double AMAZON_API_COST_PER_REQUEST = 0.0; // Amazon SP-API 免费

    // 每日限制
    private static final int MAX_DAILY_OPENAI_REQUESTS = 50;
    private static final int MAX_DAILY_TWITTER_REQUESTS = 1000;
    private static final int MAX_DAILY_AMAZON_REQUESTS = 100;
    private static final double MAX_DAILY_BUDGET = 10.0; // 每日最大预算10美元

    // 使用计数器
    private final AtomicInteger dailyOpenAIRequests = new AtomicInteger(0);
    private final AtomicInteger dailyTwitterRequests = new AtomicInteger(0);
    private final AtomicInteger dailyAmazonRequests = new AtomicInteger(0);
    private final AtomicReference<Double> dailyCost = new AtomicReference<>(0.0);
    private final AtomicReference<LocalDate> currentDate = new AtomicReference<>(LocalDate.now());

    public CostControlService() {
        loadDailyUsage();
    }

    /**
     * 检查是否可以进行OpenAI API调用
     */
    public boolean canMakeOpenAIRequest(boolean isHD) {
        checkAndResetDailyCounters();

        double requestCost = isHD ? OPENAI_DALLE3_HD_COST_PER_IMAGE : OPENAI_DALLE3_COST_PER_IMAGE;

        // 检查请求次数限制
        if (dailyOpenAIRequests.get() >= MAX_DAILY_OPENAI_REQUESTS) {
            System.err.println("⚠️ 已达到每日OpenAI请求限制: " + MAX_DAILY_OPENAI_REQUESTS);
            return false;
        }

        // 检查预算限制
        if (dailyCost.get() + requestCost > MAX_DAILY_BUDGET) {
            System.err.println("⚠️ 请求将超出每日预算限制: $" + MAX_DAILY_BUDGET);
            System.err.println("当前已用: $" + String.format("%.2f", dailyCost.get()) +
                             ", 请求成本: $" + String.format("%.2f", requestCost));
            return false;
        }

        return true;
    }

    /**
     * 记录OpenAI API调用
     */
    public void recordOpenAIRequest(boolean isHD, boolean success) {
        checkAndResetDailyCounters();

        if (success) {
            dailyOpenAIRequests.incrementAndGet();
            double requestCost = isHD ? OPENAI_DALLE3_HD_COST_PER_IMAGE : OPENAI_DALLE3_COST_PER_IMAGE;
            dailyCost.updateAndGet(current -> current + requestCost);

            System.out.println("💰 OpenAI请求已记录 - 成本: $" + String.format("%.2f", requestCost));
            System.out.println("📊 今日统计 - 请求: " + dailyOpenAIRequests.get() + "/" + MAX_DAILY_OPENAI_REQUESTS +
                             ", 成本: $" + String.format("%.2f", dailyCost.get()) + "/$" + MAX_DAILY_BUDGET);
        }

        saveDailyUsage();
    }

    /**
     * 记录Gemini API调用
     */
    public void recordGeminiRequest(boolean success) {
        checkAndResetDailyCounters();

        if (success) {
            dailyOpenAIRequests.incrementAndGet(); // 使用同一个计数器
            dailyCost.updateAndGet(current -> current + GEMINI_COST_PER_IMAGE);

            System.out.println("💰 Gemini请求已记录 - 成本: $" + String.format("%.4f", GEMINI_COST_PER_IMAGE));
            System.out.println("📊 今日统计 - 请求: " + dailyOpenAIRequests.get() + "/" + MAX_DAILY_OPENAI_REQUESTS +
                             ", 成本: $" + String.format("%.2f", dailyCost.get()) + "/$" + MAX_DAILY_BUDGET);
        }

        saveDailyUsage();
    }

    /**
     * 检查是否可以进行Twitter API调用
     */
    public boolean canMakeTwitterRequest() {
        checkAndResetDailyCounters();

        if (dailyTwitterRequests.get() >= MAX_DAILY_TWITTER_REQUESTS) {
            System.err.println("⚠️ 已达到每日Twitter请求限制: " + MAX_DAILY_TWITTER_REQUESTS);
            return false;
        }

        return true;
    }

    /**
     * 记录Twitter API调用
     */
    public void recordTwitterRequest(boolean success) {
        checkAndResetDailyCounters();

        if (success) {
            dailyTwitterRequests.incrementAndGet();
            System.out.println("📱 Twitter请求已记录 - 今日请求: " + dailyTwitterRequests.get() + "/" + MAX_DAILY_TWITTER_REQUESTS);
        }

        saveDailyUsage();
    }

    /**
     * 检查是否可以进行Amazon API调用
     */
    public boolean canMakeAmazonRequest() {
        checkAndResetDailyCounters();

        if (dailyAmazonRequests.get() >= MAX_DAILY_AMAZON_REQUESTS) {
            System.err.println("⚠️ 已达到每日Amazon请求限制: " + MAX_DAILY_AMAZON_REQUESTS);
            return false;
        }

        return true;
    }

    /**
     * 记录Amazon API调用
     */
    public void recordAmazonRequest(boolean success) {
        checkAndResetDailyCounters();

        if (success) {
            dailyAmazonRequests.incrementAndGet();
            System.out.println("🛒 Amazon请求已记录 - 今日请求: " + dailyAmazonRequests.get() + "/" + MAX_DAILY_AMAZON_REQUESTS);
        }

        saveDailyUsage();
    }

    /**
     * 获取今日使用统计
     */
    public UsageStats getTodayUsageStats() {
        checkAndResetDailyCounters();

        return new UsageStats(
            dailyOpenAIRequests.get(),
            MAX_DAILY_OPENAI_REQUESTS,
            dailyTwitterRequests.get(),
            MAX_DAILY_TWITTER_REQUESTS,
            dailyAmazonRequests.get(),
            MAX_DAILY_AMAZON_REQUESTS,
            dailyCost.get(),
            MAX_DAILY_BUDGET
        );
    }

    /**
     * 获取可以处理的最大话题数量
     * 基于剩余的OpenAI请求配额和预算
     */
    public int getMaxTopicsCanProcess() {
        checkAndResetDailyCounters();

        // 基于请求次数限制
        int remainingRequests = MAX_DAILY_OPENAI_REQUESTS - dailyOpenAIRequests.get();

        // 基于预算限制（使用标准质量成本计算）
        double remainingBudget = MAX_DAILY_BUDGET - dailyCost.get();
        int maxTopicsByBudget = (int) Math.floor(remainingBudget / OPENAI_DALLE3_COST_PER_IMAGE);

        // 取两者中的较小值
        int maxTopics = Math.min(remainingRequests, maxTopicsByBudget);

        // 确保不为负数
        return Math.max(0, maxTopics);
    }

    /**
     * 获取可以处理的最大话题数量（指定质量）
     */
    public int getMaxTopicsCanProcess(boolean isHD) {
        checkAndResetDailyCounters();

        // 基于请求次数限制
        int remainingRequests = MAX_DAILY_OPENAI_REQUESTS - dailyOpenAIRequests.get();

        // 基于预算限制
        double remainingBudget = MAX_DAILY_BUDGET - dailyCost.get();
        double costPerImage = isHD ? OPENAI_DALLE3_HD_COST_PER_IMAGE : OPENAI_DALLE3_COST_PER_IMAGE;
        int maxTopicsByBudget = (int) Math.floor(remainingBudget / costPerImage);

        // 取两者中的较小值
        int maxTopics = Math.min(remainingRequests, maxTopicsByBudget);

        // 确保不为负数
        return Math.max(0, maxTopics);
    }

    /**
     * 获取使用Gemini可以处理的最大话题数量
     */
    public int getMaxTopicsCanProcessWithGemini() {
        checkAndResetDailyCounters();

        // 基于请求次数限制
        int remainingRequests = MAX_DAILY_OPENAI_REQUESTS - dailyOpenAIRequests.get();

        // 基于预算限制（Gemini成本更低）
        double remainingBudget = MAX_DAILY_BUDGET - dailyCost.get();
        int maxTopicsByBudget = (int) Math.floor(remainingBudget / GEMINI_COST_PER_IMAGE);

        // 取两者中的较小值
        int maxTopics = Math.min(remainingRequests, maxTopicsByBudget);

        // 确保不为负数
        return Math.max(0, maxTopics);
    }

    /**
     * 检查是否有足够的配额处理指定数量的话题
     */
    public boolean canProcessTopics(int topicCount, boolean isHD) {
        return getMaxTopicsCanProcess(isHD) >= topicCount;
    }

    /**
     * 获取剩余配额信息
     */
    public QuotaInfo getQuotaInfo() {
        checkAndResetDailyCounters();

        int remainingRequests = MAX_DAILY_OPENAI_REQUESTS - dailyOpenAIRequests.get();
        double remainingBudget = MAX_DAILY_BUDGET - dailyCost.get();

        return new QuotaInfo(
            remainingRequests,
            MAX_DAILY_OPENAI_REQUESTS,
            remainingBudget,
            MAX_DAILY_BUDGET,
            getMaxTopicsCanProcess(false),
            getMaxTopicsCanProcess(true)
        );
    }

    /**
     * 打印使用统计
     */
    public void printUsageStats() {
        UsageStats stats = getTodayUsageStats();
        QuotaInfo quota = getQuotaInfo();

        System.out.println("\n📊 今日API使用统计 (" + LocalDate.now() + ")");
        System.out.println("=");
        System.out.println("🎨 OpenAI (图片生成): " + stats.openAIRequests + "/" + stats.maxOpenAIRequests +
                         " (" + String.format("%.1f", stats.getOpenAIUsagePercentage()) + "%)");
        System.out.println("📱 Twitter (热搜获取): " + stats.twitterRequests + "/" + stats.maxTwitterRequests +
                         " (" + String.format("%.1f", stats.getTwitterUsagePercentage()) + "%)");
        System.out.println("🛒 Amazon (商品上架): " + stats.amazonRequests + "/" + stats.maxAmazonRequests +
                         " (" + String.format("%.1f", stats.getAmazonUsagePercentage()) + "%)");
        System.out.println("💰 成本: $" + String.format("%.2f", stats.dailyCost) + "/$" + String.format("%.2f", stats.maxDailyBudget) +
                         " (" + String.format("%.1f", stats.getCostUsagePercentage()) + "%)");
        System.out.println("\n🎯 可处理话题数:");
        System.out.println("   标准质量: " + quota.maxTopicsStandard + " 个");
        System.out.println("   高清质量: " + quota.maxTopicsHD + " 个");
        System.out.println("=");
    }

    /**
     * 检查并重置每日计数器
     */
    private void checkAndResetDailyCounters() {
        LocalDate today = LocalDate.now();

        if (!today.equals(currentDate.get())) {
            System.out.println("🔄 新的一天开始，重置使用计数器");
            currentDate.set(today);
            dailyOpenAIRequests.set(0);
            dailyTwitterRequests.set(0);
            dailyAmazonRequests.set(0);
            dailyCost.set(0.0);
            saveDailyUsage();
        }
    }

    /**
     * 保存每日使用情况到文件
     */
    private void saveDailyUsage() {
        try {
            Map<String, String> usage = new HashMap<>();
            usage.put("date", currentDate.get().toString());
            usage.put("openai_requests", String.valueOf(dailyOpenAIRequests.get()));
            usage.put("twitter_requests", String.valueOf(dailyTwitterRequests.get()));
            usage.put("amazon_requests", String.valueOf(dailyAmazonRequests.get()));
            usage.put("daily_cost", String.valueOf(dailyCost.get()));
            usage.put("last_updated", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

            StringBuilder content = new StringBuilder();
            for (Map.Entry<String, String> entry : usage.entrySet()) {
                content.append(entry.getKey()).append("=").append(entry.getValue()).append("\n");
            }

            Files.write(Paths.get(QUOTA_FILE), content.toString().getBytes());

        } catch (IOException e) {
            System.err.println("⚠️ 保存使用统计失败: " + e.getMessage());
        }
    }

    /**
     * 从文件加载每日使用情况
     */
    private void loadDailyUsage() {
        try {
            Path quotaPath = Paths.get(QUOTA_FILE);
            if (!Files.exists(quotaPath)) {
                return;
            }

            Map<String, String> usage = new HashMap<>();
            Files.readAllLines(quotaPath).forEach(line -> {
                String[] parts = line.split("=", 2);
                if (parts.length == 2) {
                    usage.put(parts[0].trim(), parts[1].trim());
                }
            });

            String dateStr = usage.get("date");
            if (dateStr != null && LocalDate.parse(dateStr).equals(LocalDate.now())) {
                // 如果是今天的数据，加载计数器
                dailyOpenAIRequests.set(Integer.parseInt(usage.getOrDefault("openai_requests", "0")));
                dailyTwitterRequests.set(Integer.parseInt(usage.getOrDefault("twitter_requests", "0")));
                dailyAmazonRequests.set(Integer.parseInt(usage.getOrDefault("amazon_requests", "0")));
                dailyCost.set(Double.parseDouble(usage.getOrDefault("daily_cost", "0.0")));
                currentDate.set(LocalDate.parse(dateStr));

                System.out.println("📊 已加载今日使用统计");
            }

        } catch (Exception e) {
            System.err.println("⚠️ 加载使用统计失败: " + e.getMessage());
        }
    }

    /**
     * 配额信息类
     */
    public static class QuotaInfo {
        public final int remainingRequests;
        public final int maxRequests;
        public final double remainingBudget;
        public final double maxBudget;
        public final int maxTopicsStandard;
        public final int maxTopicsHD;

        public QuotaInfo(int remainingRequests, int maxRequests,
                        double remainingBudget, double maxBudget,
                        int maxTopicsStandard, int maxTopicsHD) {
            this.remainingRequests = remainingRequests;
            this.maxRequests = maxRequests;
            this.remainingBudget = remainingBudget;
            this.maxBudget = maxBudget;
            this.maxTopicsStandard = maxTopicsStandard;
            this.maxTopicsHD = maxTopicsHD;
        }

        public double getRequestUsagePercentage() {
            return maxRequests > 0 ? ((maxRequests - remainingRequests) * 100.0 / maxRequests) : 0;
        }

        public double getBudgetUsagePercentage() {
            return maxBudget > 0 ? ((maxBudget - remainingBudget) * 100.0 / maxBudget) : 0;
        }

        public boolean isNearRequestLimit() {
            return getRequestUsagePercentage() > 80;
        }

        public boolean isNearBudgetLimit() {
            return getBudgetUsagePercentage() > 80;
        }
    }

    /**
     * 使用统计数据类
     */
    public static class UsageStats {
        public final int openAIRequests;
        public final int maxOpenAIRequests;
        public final int twitterRequests;
        public final int maxTwitterRequests;
        public final int amazonRequests;
        public final int maxAmazonRequests;
        public final double dailyCost;
        public final double maxDailyBudget;

        public UsageStats(int openAIRequests, int maxOpenAIRequests,
                         int twitterRequests, int maxTwitterRequests,
                         int amazonRequests, int maxAmazonRequests,
                         double dailyCost, double maxDailyBudget) {
            this.openAIRequests = openAIRequests;
            this.maxOpenAIRequests = maxOpenAIRequests;
            this.twitterRequests = twitterRequests;
            this.maxTwitterRequests = maxTwitterRequests;
            this.amazonRequests = amazonRequests;
            this.maxAmazonRequests = maxAmazonRequests;
            this.dailyCost = dailyCost;
            this.maxDailyBudget = maxDailyBudget;
        }

        public double getOpenAIUsagePercentage() {
            return maxOpenAIRequests > 0 ? (openAIRequests * 100.0 / maxOpenAIRequests) : 0;
        }

        public double getTwitterUsagePercentage() {
            return maxTwitterRequests > 0 ? (twitterRequests * 100.0 / maxTwitterRequests) : 0;
        }

        public double getAmazonUsagePercentage() {
            return maxAmazonRequests > 0 ? (amazonRequests * 100.0 / maxAmazonRequests) : 0;
        }

        public double getCostUsagePercentage() {
            return maxDailyBudget > 0 ? (dailyCost * 100.0 / maxDailyBudget) : 0;
        }

        public boolean isNearLimit() {
            return getOpenAIUsagePercentage() > 80 ||
                   getTwitterUsagePercentage() > 80 ||
                   getAmazonUsagePercentage() > 80 ||
                   getCostUsagePercentage() > 80;
        }
    }

    public static void main(String[] args) {
        // The client gets the API key from the environment variable `GEMINI_API_KEY`.
        Client client = new Client();
        GenerateContentResponse response =
                client.models.generateContent(
                        "gemini-2.5-flash",
                        "Explain how AI works in a few words",
                        null);

        System.out.println(response.text());
    }
}
