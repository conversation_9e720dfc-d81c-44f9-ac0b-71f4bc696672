package com.hal.service;

import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;

/**
 * 重试服务
 * 提供通用的重试机制，用于处理API调用失败等情况
 */
public class RetryService {
    
    private static final int DEFAULT_MAX_ATTEMPTS = 3;
    private static final long DEFAULT_DELAY_MS = 1000;
    private static final double DEFAULT_BACKOFF_MULTIPLIER = 2.0;
    
    /**
     * 重试配置
     */
    public static class RetryConfig {
        private int maxAttempts = DEFAULT_MAX_ATTEMPTS;
        private long delayMs = DEFAULT_DELAY_MS;
        private double backoffMultiplier = DEFAULT_BACKOFF_MULTIPLIER;
        private Predicate<Exception> retryCondition = ex -> true;
        
        public RetryConfig maxAttempts(int maxAttempts) {
            this.maxAttempts = maxAttempts;
            return this;
        }
        
        public RetryConfig delay(long delayMs) {
            this.delayMs = delayMs;
            return this;
        }
        
        public RetryConfig backoffMultiplier(double multiplier) {
            this.backoffMultiplier = multiplier;
            return this;
        }
        
        public RetryConfig retryOn(Predicate<Exception> condition) {
            this.retryCondition = condition;
            return this;
        }
        
        // Getters
        public int getMaxAttempts() { return maxAttempts; }
        public long getDelayMs() { return delayMs; }
        public double getBackoffMultiplier() { return backoffMultiplier; }
        public Predicate<Exception> getRetryCondition() { return retryCondition; }
    }
    
    /**
     * 执行带重试的操作
     */
    public static <T> T executeWithRetry(Callable<T> operation, RetryConfig config) throws Exception {
        Exception lastException = null;
        long currentDelay = config.getDelayMs();
        
        for (int attempt = 1; attempt <= config.getMaxAttempts(); attempt++) {
            try {
                System.out.println("尝试执行操作，第 " + attempt + " 次...");
                return operation.call();
                
            } catch (Exception e) {
                lastException = e;
                
                // 检查是否应该重试
                if (!config.getRetryCondition().test(e)) {
                    System.err.println("异常不满足重试条件，停止重试: " + e.getMessage());
                    throw e;
                }
                
                // 如果是最后一次尝试，直接抛出异常
                if (attempt == config.getMaxAttempts()) {
                    System.err.println("已达到最大重试次数 (" + config.getMaxAttempts() + ")，停止重试");
                    break;
                }
                
                // 等待后重试
                System.err.println("第 " + attempt + " 次尝试失败: " + e.getMessage());
                System.out.println("等待 " + currentDelay + "ms 后重试...");
                
                try {
                    TimeUnit.MILLISECONDS.sleep(currentDelay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试被中断", ie);
                }
                
                // 增加延迟时间（指数退避）
                currentDelay = (long) (currentDelay * config.getBackoffMultiplier());
            }
        }
        
        throw lastException;
    }
    
    /**
     * 使用默认配置执行重试
     */
    public static <T> T executeWithRetry(Callable<T> operation) throws Exception {
        return executeWithRetry(operation, new RetryConfig());
    }
    
    /**
     * 针对OpenAI API的重试配置
     */
    public static RetryConfig openAIRetryConfig() {
        return new RetryConfig()
            .maxAttempts(3)
            .delay(2000)
            .backoffMultiplier(2.0)
            .retryOn(ex -> {
                String message = ex.getMessage().toLowerCase();
                // 重试条件：网络错误、超时、服务器错误、限流
                return message.contains("timeout") ||
                       message.contains("connection") ||
                       message.contains("500") ||
                       message.contains("502") ||
                       message.contains("503") ||
                       message.contains("504") ||
                       message.contains("429"); // Rate limit
            });
    }
    
    /**
     * 针对Twitter API的重试配置
     */
    public static RetryConfig twitterRetryConfig() {
        return new RetryConfig()
            .maxAttempts(3)
            .delay(15000) // Twitter API限制通常需要等待15秒
            .backoffMultiplier(1.5)
            .retryOn(ex -> {
                String message = ex.getMessage().toLowerCase();
                // 重试条件：限流、服务器错误
                return message.contains("429") ||
                       message.contains("500") ||
                       message.contains("502") ||
                       message.contains("503") ||
                       message.contains("timeout");
            });
    }
    
    /**
     * 针对Amazon API的重试配置
     */
    public static RetryConfig amazonRetryConfig() {
        return new RetryConfig()
            .maxAttempts(5)
            .delay(1000)
            .backoffMultiplier(2.0)
            .retryOn(ex -> {
                String message = ex.getMessage().toLowerCase();
                // 重试条件：限流、服务器错误、网络错误
                return message.contains("throttl") ||
                       message.contains("429") ||
                       message.contains("500") ||
                       message.contains("502") ||
                       message.contains("503") ||
                       message.contains("504") ||
                       message.contains("timeout") ||
                       message.contains("connection");
            });
    }
    
    /**
     * 执行操作并记录详细日志
     */
    public static <T> T executeWithDetailedLogging(String operationName, Callable<T> operation, RetryConfig config) throws Exception {
        System.out.println("🔄 开始执行: " + operationName);
        long startTime = System.currentTimeMillis();
        
        try {
            T result = executeWithRetry(operation, config);
            long duration = System.currentTimeMillis() - startTime;
            System.out.println("✅ " + operationName + " 执行成功，耗时: " + duration + "ms");
            return result;
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            System.err.println("❌ " + operationName + " 执行失败，耗时: " + duration + "ms");
            System.err.println("错误详情: " + e.getMessage());
            throw e;
        }
    }
}
