package com.hal.service.database;

import com.hal.database.DatabaseConfig;
import com.hal.entity.TrendingTopic;
import com.hal.entity.TopicFrequency;
import com.hal.entity.TopicTypeStats;
import com.hal.mapper.TrendingTopicMapper;
import org.apache.ibatis.session.SqlSession;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 热搜话题业务服务类
 */
public class TrendingTopicService {

    private final DatabaseConfig databaseConfig;

    public TrendingTopicService() {
        this.databaseConfig = DatabaseConfig.getInstance();
    }

    public TrendingTopicService(DatabaseConfig databaseConfig) {
        this.databaseConfig = databaseConfig;
    }
    /**
     * 批量保存热搜话题
     */
    public List<TrendingTopic> saveTrendingTopics(Long taskId, List<String> topics, String source) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            TrendingTopicMapper mapper = session.getMapper(TrendingTopicMapper.class);

            List<TrendingTopic> savedTopics = new java.util.ArrayList<>();

            for (int i = 0; i < topics.size(); i++) {
                String topic = topics.get(i);

                // 检查是否已存在（去重）
                if (mapper.countByTopicAndDate(topic, LocalDateTime.now()) > 0) {
                    System.out.println("⚠️ 话题已存在，跳过: " + topic);
                    continue;
                }

                TrendingTopic trendingTopic = new TrendingTopic(topic, i + 1, source);
                trendingTopic.setTaskId(taskId);
                trendingTopic.setLanguage(detectLanguage(topic));
                trendingTopic.setTopicType(detectTopicType(topic));

                mapper.insert(trendingTopic);
                savedTopics.add(trendingTopic);
            }

            session.commit();
            System.out.println("✅ 保存了 " + savedTopics.size() + " 个热搜话题");
            return savedTopics;

        } catch (Exception e) {
            System.err.println("❌ 保存热搜话题失败: " + e.getMessage());
            throw new RuntimeException("Failed to save trending topics", e);
        }
    }

    /**
     * 更新话题状态
     */
    public void updateTopicStatus(Long topicId, String status) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            TrendingTopicMapper mapper = session.getMapper(TrendingTopicMapper.class);
            mapper.updateStatus(topicId, status);
            session.commit();
        } catch (Exception e) {
            System.err.println("❌ 更新话题状态失败: " + e.getMessage());
        }
    }

    /**
     * 更新话题类型
     */
    public void updateTopicType(Long topicId, String topicType) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            TrendingTopicMapper mapper = session.getMapper(TrendingTopicMapper.class);
            mapper.updateTopicType(topicId, topicType);
            session.commit();
        } catch (Exception e) {
            System.err.println("❌ 更新话题类型失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询话题
     */
    public TrendingTopic getTopicById(Long topicId) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            TrendingTopicMapper mapper = session.getMapper(TrendingTopicMapper.class);
            return mapper.findById(topicId);
        } catch (Exception e) {
            System.err.println("❌ 查询话题失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 根据任务ID查询话题列表
     */
    public List<TrendingTopic> getTopicsByTaskId(Long taskId) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            TrendingTopicMapper mapper = session.getMapper(TrendingTopicMapper.class);
            return mapper.findByTaskId(taskId);
        } catch (Exception e) {
            System.err.println("❌ 查询任务话题失败: " + e.getMessage());
            return java.util.Collections.emptyList();
        }
    }

    /**
     * 根据状态查询话题列表
     */
    public List<TrendingTopic> getTopicsByStatus(String status) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            TrendingTopicMapper mapper = session.getMapper(TrendingTopicMapper.class);

            // 如果状态为空或空字符串，返回所有话题
            if (status == null || status.trim().isEmpty()) {
                return getAllTopics();
            }

            return mapper.findByStatus(status);
        } catch (Exception e) {
            System.err.println("❌ 查询指定状态话题失败: " + e.getMessage());
            return java.util.Collections.emptyList();
        }
    }

    /**
     * 获取所有话题
     */
    public List<TrendingTopic> getAllTopics() {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            TrendingTopicMapper mapper = session.getMapper(TrendingTopicMapper.class);
            // 使用查询最近的话题，限制数量避免返回过多数据
            return mapper.findRecent(1000); // 最近1000条
        } catch (Exception e) {
            System.err.println("❌ 查询所有话题失败: " + e.getMessage());
            return java.util.Collections.emptyList();
        }
    }

    /**
     * 查询待处理的话题
     */
    public List<TrendingTopic> getPendingTopics() {
        return getTopicsByStatus("PENDING");
    }

    /**
     * 查询指定类型的话题
     */
    public List<TrendingTopic> getTopicsByType(String topicType, int limit) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            TrendingTopicMapper mapper = session.getMapper(TrendingTopicMapper.class);
            return mapper.findByTopicType(topicType, limit);
        } catch (Exception e) {
            System.err.println("❌ 查询指定类型话题失败: " + e.getMessage());
            return List.of();
        }
    }

    /**
     * 搜索话题
     */
    public List<TrendingTopic> searchTopics(String keyword, int limit) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            TrendingTopicMapper mapper = session.getMapper(TrendingTopicMapper.class);
            return mapper.searchByKeyword(keyword, limit);
        } catch (Exception e) {
            System.err.println("❌ 搜索话题失败: " + e.getMessage());
            return List.of();
        }
    }

    /**
     * 获取热门话题
     */
    public List<TopicFrequency> getPopularTopics(int days, int minFrequency, int limit) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            TrendingTopicMapper mapper = session.getMapper(TrendingTopicMapper.class);
            LocalDateTime startDate = LocalDateTime.now().minusDays(days);
            return mapper.findPopularTopics(startDate, minFrequency, limit);
        } catch (Exception e) {
            System.err.println("❌ 获取热门话题失败: " + e.getMessage());
            return List.of();
        }
    }

    /**
     * 获取话题类型统计
     */
    public List<TopicTypeStats> getTopicTypeStatistics(LocalDateTime startDate, LocalDateTime endDate) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            TrendingTopicMapper mapper = session.getMapper(TrendingTopicMapper.class);
            return mapper.getTopicTypeStatistics(startDate, endDate);
        } catch (Exception e) {
            System.err.println("❌ 获取话题类型统计失败: " + e.getMessage());
            return List.of();
        }
    }

    /**
     * 检测话题语言
     */
    private String detectLanguage(String topic) {
        // 简单的语言检测逻辑
        if (topic.matches(".*[\\u4e00-\\u9fff].*")) {
            return "zh"; // 包含中文字符
        } else {
            return "en"; // 默认英文
        }
    }

    /**
     * 检测话题类型
     */
    private String detectTopicType(String topic) {
        String lowerTopic = topic.toLowerCase();

        // 科技类关键词
        if (containsAny(lowerTopic, "ai", "artificial intelligence", "robot", "tech", "digital", "cyber", "algorithm", "machine learning", "人工智能", "机器人", "科技", "数字", "算法")) {
            return "technology";
        }

        // 环境类关键词
        if (containsAny(lowerTopic, "climate", "environment", "green", "carbon", "pollution", "renewable", "气候", "环境", "绿色", "碳", "污染", "可再生")) {
            return "environment";
        }

        // 政治类关键词
        if (containsAny(lowerTopic, "election", "government", "policy", "politics", "vote", "democracy", "选举", "政府", "政策", "政治", "投票", "民主")) {
            return "politics";
        }

        // 金融类关键词
        if (containsAny(lowerTopic, "crypto", "bitcoin", "stock", "market", "finance", "economy", "investment", "加密货币", "比特币", "股票", "市场", "金融", "经济", "投资")) {
            return "finance";
        }

        // 娱乐类关键词
        if (containsAny(lowerTopic, "celebrity", "movie", "music", "entertainment", "hollywood", "star", "明星", "电影", "音乐", "娱乐", "好莱坞", "演员")) {
            return "entertainment";
        }

        // 体育类关键词
        if (containsAny(lowerTopic, "sports", "football", "basketball", "soccer", "olympics", "championship", "体育", "足球", "篮球", "奥运", "冠军", "比赛")) {
            return "sports";
        }

        return "general";
    }

    /**
     * 检查字符串是否包含任意一个关键词
     */
    private boolean containsAny(String text, String... keywords) {
        for (String keyword : keywords) {
            if (text.contains(keyword)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 清理旧话题记录
     */
    public int cleanupOldTopics(int retentionDays) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            TrendingTopicMapper mapper = session.getMapper(TrendingTopicMapper.class);
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(retentionDays);
            int deletedCount = mapper.deleteBeforeDate(cutoffDate);
            session.commit();

            System.out.println("🧹 清理了 " + deletedCount + " 条旧话题记录");
            return deletedCount;
        } catch (Exception e) {
            System.err.println("❌ 清理旧话题失败: " + e.getMessage());
            return 0;
        }
    }

    /**
     * 打印话题统计报告
     */
    public void printTopicReport(Long taskId) {
        List<TrendingTopic> topics = getTopicsByTaskId(taskId);
        if (topics.isEmpty()) {
            System.out.println("📋 该任务没有话题记录");
            return;
        }

        System.out.println("\n📋 话题处理报告");
        System.out.println("=".repeat(50));
        System.out.println("任务ID: " + taskId);
        System.out.println("话题总数: " + topics.size());

        // 按状态统计
        var statusCount = topics.stream()
            .collect(java.util.stream.Collectors.groupingBy(
                TrendingTopic::getStatus,
                java.util.stream.Collectors.counting()
            ));

        statusCount.forEach((status, count) ->
            System.out.println(getStatusEmoji(status) + " " + status + ": " + count)
        );

        // 按类型统计
        var typeCount = topics.stream()
            .collect(java.util.stream.Collectors.groupingBy(
                topic -> topic.getTopicType() != null ? topic.getTopicType() : "unknown",
                java.util.stream.Collectors.counting()
            ));

        System.out.println("\n📊 话题类型分布:");
        typeCount.forEach((type, count) ->
            System.out.println("  " + getTypeEmoji(type) + " " + type + ": " + count)
        );

        System.out.println("=".repeat(50));
    }

    /**
     * 获取状态对应的表情符号
     */
    private String getStatusEmoji(String status) {
        switch (status) {
            case "COMPLETED":
                return "✅";
            case "FAILED":
                return "❌";
            case "PROCESSING":
                return "🔄";
            case "PENDING":
                return "⏳";
            case "SKIPPED":
                return "⏭️";
            default:
                return "❓";
        }
    }

    /**
     * 获取类型对应的表情符号
     */
    private String getTypeEmoji(String type) {
        return switch (type) {
            case "technology" -> "💻";
            case "environment" -> "🌍";
            case "politics" -> "🏛️";
            case "finance" -> "💰";
            case "entertainment" -> "🎬";
            case "sports" -> "⚽";
            case "general" -> "📰";
            default -> "❓";
        };
    }
}
