package com.hal.service.database;

import com.hal.database.DatabaseConfig;
import com.hal.entity.ApiUsageLog;
import com.hal.entity.ApiUsageStats;
import com.hal.entity.DailyApiStats;
import com.hal.entity.ErrorAnalysis;
import com.hal.entity.QuotaUsage;
import com.hal.mapper.ApiUsageLogMapper;
import org.apache.ibatis.session.SqlSession;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * API使用日志业务服务类
 */
public class ApiUsageLogService {

    private final DatabaseConfig databaseConfig;

    public ApiUsageLogService() {
        this.databaseConfig = DatabaseConfig.getInstance();
    }

    /**
     * 记录API请求开始
     */
    public ApiUsageLog logRequestStart(Long taskId, String apiProvider, String endpoint, String method, String requestParams) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            ApiUsageLogMapper mapper = session.getMapper(ApiUsageLogMapper.class);

            ApiUsageLog log = new ApiUsageLog(apiProvider, endpoint, method);
            log.setTaskId(taskId);
            log.setRequestParams(requestParams);
            log.setResponseStatus("PENDING");

            mapper.insert(log);
            session.commit();

            return log;
        } catch (Exception e) {
            System.err.println("❌ 记录API请求开始失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 记录API请求完成
     */
    public void logRequestComplete(Long logId, String responseStatus, Integer httpStatusCode,
                                 String responseMessage, Long responseTimeMs, Double cost) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            ApiUsageLogMapper mapper = session.getMapper(ApiUsageLogMapper.class);

            mapper.updateResponse(logId, responseStatus, httpStatusCode, responseMessage,
                                responseTimeMs, null, null, LocalDateTime.now());

            // 如果有成本信息，更新成本
            if (cost != null) {
                ApiUsageLog log = mapper.findById(logId);
                if (log != null) {
                    log.setCost(cost);
                    // 这里需要添加更新成本的方法到mapper
                }
            }

            session.commit();
        } catch (Exception e) {
            System.err.println("❌ 记录API请求完成失败: " + e.getMessage());
        }
    }

    /**
     * 记录API请求失败
     */
    public void logRequestFailed(Long logId, String errorCode, String errorMessage, Long responseTimeMs) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            ApiUsageLogMapper mapper = session.getMapper(ApiUsageLogMapper.class);

            mapper.updateResponse(logId, "FAILED", null, null, responseTimeMs,
                                errorCode, errorMessage, LocalDateTime.now());
            session.commit();
        } catch (Exception e) {
            System.err.println("❌ 记录API请求失败失败: " + e.getMessage());
        }
    }

    /**
     * 更新重试次数
     */
    public void updateRetryCount(Long logId, Integer retryCount) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            ApiUsageLogMapper mapper = session.getMapper(ApiUsageLogMapper.class);
            mapper.updateRetryCount(logId, retryCount);
            session.commit();
        } catch (Exception e) {
            System.err.println("❌ 更新重试次数失败: " + e.getMessage());
        }
    }

    /**
     * 获取今日API使用统计
     */
    public List<DailyApiStats> getTodayUsageStats() {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            ApiUsageLogMapper mapper = session.getMapper(ApiUsageLogMapper.class);
            LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
            LocalDateTime endOfDay = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59);
            return mapper.getDailyStatistics(startOfDay, endOfDay);
        } catch (Exception e) {
            System.err.println("❌ 获取今日使用统计失败: " + e.getMessage());
            return List.of();
        }
    }

    /**
     * 获取API使用统计
     */
    public List<ApiUsageStats> getUsageStatistics(LocalDateTime startDate, LocalDateTime endDate) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            ApiUsageLogMapper mapper = session.getMapper(ApiUsageLogMapper.class);
            return mapper.getUsageStatistics(startDate, endDate);
        } catch (Exception e) {
            System.err.println("❌ 获取使用统计失败: " + e.getMessage());
            return List.of();
        }
    }

    /**
     * 获取错误分析
     */
    public List<ErrorAnalysis> getErrorAnalysis(int days) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            ApiUsageLogMapper mapper = session.getMapper(ApiUsageLogMapper.class);
            LocalDateTime startDate = LocalDateTime.now().minusDays(days);
            return mapper.getErrorAnalysis(startDate);
        } catch (Exception e) {
            System.err.println("❌ 获取错误分析失败: " + e.getMessage());
            return List.of();
        }
    }

    /**
     * 获取当前配额使用情况
     */
    public List<QuotaUsage> getCurrentQuotaUsage() {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            ApiUsageLogMapper mapper = session.getMapper(ApiUsageLogMapper.class);
            return mapper.getCurrentQuotaUsage();
        } catch (Exception e) {
            System.err.println("❌ 获取配额使用情况失败: " + e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询慢请求
     */
    public List<ApiUsageLog> getSlowRequests(long thresholdMs, int limit) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            ApiUsageLogMapper mapper = session.getMapper(ApiUsageLogMapper.class);
            return mapper.findSlowRequests(thresholdMs, limit);
        } catch (Exception e) {
            System.err.println("❌ 查询慢请求失败: " + e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询最近错误
     */
    public List<ApiUsageLog> getRecentErrors(int days, int limit) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            ApiUsageLogMapper mapper = session.getMapper(ApiUsageLogMapper.class);
            LocalDateTime startDate = LocalDateTime.now().minusDays(days);
            return mapper.findRecentErrors(startDate, limit);
        } catch (Exception e) {
            System.err.println("❌ 查询最近错误失败: " + e.getMessage());
            return List.of();
        }
    }

    /**
     * 清理旧日志
     */
    public int cleanupOldLogs(int retentionDays) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            ApiUsageLogMapper mapper = session.getMapper(ApiUsageLogMapper.class);
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(retentionDays);

            // 先清理成功的日志（保留时间较短）
            int successDeleted = mapper.cleanupSuccessLogs(cutoffDate);

            // 再清理所有旧日志（错误日志保留更长时间）
            LocalDateTime errorCutoffDate = LocalDateTime.now().minusDays(retentionDays * 2);
            int totalDeleted = mapper.deleteBeforeDate(errorCutoffDate);

            session.commit();

            System.out.println("🧹 清理了 " + successDeleted + " 条成功日志，" + (totalDeleted - successDeleted) + " 条错误日志");
            return totalDeleted;
        } catch (Exception e) {
            System.err.println("❌ 清理旧日志失败: " + e.getMessage());
            return 0;
        }
    }

    /**
     * 打印API使用报告
     */
    public void printUsageReport(int days) {
        System.out.println("\n📊 API使用报告 (最近" + days + "天)");
        System.out.println("=".repeat(60));

        LocalDateTime startDate = LocalDateTime.now().minusDays(days);
        LocalDateTime endDate = LocalDateTime.now();

        // 获取使用统计
        List<ApiUsageStats> stats = getUsageStatistics(startDate, endDate);

        if (stats.isEmpty()) {
            System.out.println("📭 没有API使用记录");
            return;
        }

        // 按API提供商分组显示
        Map<String, List<ApiUsageStats>> providerStats = stats.stream()
            .collect(java.util.stream.Collectors.groupingBy(
                ApiUsageStats::getApiProvider
            ));

        providerStats.forEach((provider, providerStatsList) -> {
            System.out.println("\n🔌 " + provider + " API:");

            long totalRequests = providerStatsList.stream()
                .mapToLong(ApiUsageStats::getRequestCount)
                .sum();

            double totalCost = providerStatsList.stream()
                .mapToDouble(s -> s.getTotalCost() != null ? s.getTotalCost() : 0)
                .sum();

            double avgResponseTime = providerStatsList.stream()
                .mapToDouble(s -> s.getAvgResponseTime() != null ? s.getAvgResponseTime() : 0)
                .average().orElse(0);

            System.out.println("  总请求数: " + totalRequests);
            System.out.println("  总成本: $" + String.format("%.4f", totalCost));
            System.out.println("  平均响应时间: " + String.format("%.0f", avgResponseTime) + "ms");

            // 按状态显示
            providerStatsList.forEach(stat -> {
                String status = stat.getResponseStatus();
                String emoji = getStatusEmoji(status);
                System.out.println("    " + emoji + " " + status + ": " + stat.getRequestCount() + " 次");
            });
        });

        // 显示错误分析
        List<ErrorAnalysis> errors = getErrorAnalysis(days);
        if (!errors.isEmpty()) {
            System.out.println("\n❌ 错误分析:");
            errors.stream().limit(5).forEach(error -> {
                System.out.println("  " + error.getApiProvider() + " - " +
                                 error.getErrorCode() + ": " + error.getErrorCount() + " 次");
            });
        }

        // 显示配额使用情况
        List<QuotaUsage> quotas = getCurrentQuotaUsage();
        if (!quotas.isEmpty()) {
            System.out.println("\n📈 当前配额使用:");
            quotas.forEach(quota -> {
                double percentage = quota.getUsagePercentage();
                String bar = createProgressBar(percentage);
                System.out.println("  " + quota.getApiProvider() + ": " +
                                 quota.getCurrentQuotaUsed() + "/" + quota.getQuotaLimit() +
                                 " (" + String.format("%.1f", percentage) + "%) " + bar);
            });
        }

        System.out.println("=".repeat(60));
    }

    /**
     * 获取状态对应的表情符号
     */
    private String getStatusEmoji(String status) {
        switch (status) {
            case "SUCCESS":
                return "✅";
            case "FAILED":
                return "❌";
            case "TIMEOUT":
                return "⏰";
            case "RATE_LIMITED":
                return "🚫";
            default:
                return "❓";
        }
    }

    /**
     * 创建进度条
     */
    private String createProgressBar(double percentage) {
        int barLength = 20;
        int filled = (int) (percentage / 100 * barLength);
        StringBuilder bar = new StringBuilder("[");

        for (int i = 0; i < barLength; i++) {
            if (i < filled) {
                bar.append("█");
            } else {
                bar.append("░");
            }
        }
        bar.append("]");

        return bar.toString();
    }
}
