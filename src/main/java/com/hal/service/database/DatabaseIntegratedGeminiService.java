package com.hal.service.database;

import com.hal.entity.AiPrompt;
import com.hal.entity.GeneratedImage;
import com.hal.entity.TrendingTopic;
import com.hal.entity.StorageUsageStats;
import com.hal.mapper.AiPromptMapper;
import com.hal.mapper.GeneratedImageMapper;
import com.hal.service.GeminiImageService;
import com.hal.database.DatabaseConfig;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 集成数据库的Gemini图片生成服务
 */
@Service
public class DatabaseIntegratedGeminiService extends GeminiImageService {
    
    @Autowired
    private DatabaseConfig databaseConfig;
    
    @Autowired
    private TrendingTopicService topicService;
    
    @Value("${app.file.image-directory:images}")
    private String imageDirectory;
    
    /**
     * 为话题生成图片并保存到数据库
     */
    public GeneratedImageResult generateAndSaveImage(TrendingTopic topic) {
        try {
            // 生成提示词
            String prompt = generatePromptForTopic(topic.getTopic());
            
            // 保存提示词到数据库
            AiPrompt aiPrompt = savePromptToDatabase(topic.getId(), prompt);
            
            // 生成图片
            GeminiImageResult result = generateTShirtImage(topic.getTopic(), prompt, false);
            
            if (result.isSuccess()) {
                // 保存图片信息到数据库
                GeneratedImage image = saveImageToDatabase(topic.getId(), aiPrompt.getId(), result.getImageUrl());
                
                // 下载并保存图片文件
                downloadAndUpdateImage(image);
                
                return new GeneratedImageResult(image, aiPrompt, true, null);
            } else {
                return new GeneratedImageResult(null, aiPrompt, false, result.getErrorMessage());
            }
            
        } catch (Exception e) {
            System.err.println("❌ 为话题生成图片失败: " + e.getMessage());
            e.printStackTrace();
            return new GeneratedImageResult(null, null, false, e.getMessage());
        }
    }
    
    /**
     * 为话题生成提示词
     */
    private String generatePromptForTopic(String topic) {
        String topicType = detectTopicType(topic);
        String language = detectLanguage(topic);
        
        // 基础提示词模板
        String basePrompt = "Create a detailed description for a funny, satirical t-shirt design about: " + topic;
        
        // 根据话题类型添加特定风格
        String stylePrompt;
        switch (topicType) {
            case "technology":
                stylePrompt = "Make it tech-savvy and geeky, with programming or digital elements. ";
                break;
            case "environment":
                stylePrompt = "Include nature elements and environmental themes with a humorous twist. ";
                break;
            case "politics":
                stylePrompt = "Create a witty political satire that's clever but not offensive. ";
                break;
            case "finance":
                stylePrompt = "Add money, charts, or economic symbols in a humorous way. ";
                break;
            case "entertainment":
                stylePrompt = "Make it pop culture themed with movie or music references. ";
                break;
            case "sports":
                stylePrompt = "Include sports equipment or athletic themes with humor. ";
                break;
            default:
                stylePrompt = "Make it generally funny and relatable to current events. ";
                break;
        }
        
        // 根据语言调整提示词
        String languagePrompt = language.equals("zh") ? 
            "The design should work well for Chinese-speaking audiences. " :
            "The design should appeal to English-speaking audiences. ";
        
        // 技术要求
        String technicalPrompt = 
            "Style: Modern, clean vector art suitable for t-shirt printing. " +
            "Colors: Bold, vibrant colors that will look good on fabric. " +
            "Layout: Centered design that fits well on a t-shirt front. " +
            "Text: If including text, make it readable and catchy. " +
            "Avoid: Copyrighted characters, offensive content, or overly complex details.";
        
        return basePrompt + " " + stylePrompt + languagePrompt + technicalPrompt;
    }
    
    /**
     * 保存提示词到数据库
     */
    private AiPrompt savePromptToDatabase(Long topicId, String promptText) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            AiPromptMapper mapper = session.getMapper(AiPromptMapper.class);
            
            AiPrompt prompt = new AiPrompt(topicId, promptText, "COMPLETE");
            prompt.setModel("gemini-pro-vision");
            prompt.setQuality("standard");
            prompt.setLanguage(detectLanguage(promptText));
            prompt.setTokenCount(promptText.length() / 4); // 估算token数
            
            mapper.insert(prompt);
            session.commit();
            
            System.out.println("✅ 提示词已保存到数据库: ID=" + prompt.getId());
            return prompt;
            
        } catch (Exception e) {
            System.err.println("❌ 保存提示词失败: " + e.getMessage());
            throw new RuntimeException("保存提示词失败", e);
        }
    }
    
    /**
     * 保存图片信息到数据库
     */
    private GeneratedImage saveImageToDatabase(Long topicId, Long promptId, String originalUrl) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            GeneratedImageMapper mapper = session.getMapper(GeneratedImageMapper.class);
            
            GeneratedImage image = new GeneratedImage(topicId, promptId, originalUrl);
            image.setQuality("standard");
            image.setStatus("GENERATED");
            image.setGenerationCost(0.0025); // Gemini成本
            
            mapper.insert(image);
            session.commit();
            
            System.out.println("✅ 图片信息已保存到数据库: ID=" + image.getId());
            return image;
            
        } catch (Exception e) {
            System.err.println("❌ 保存图片信息失败: " + e.getMessage());
            throw new RuntimeException("保存图片信息失败", e);
        }
    }
    
    /**
     * 下载并更新图片文件信息
     */
    private void downloadAndUpdateImage(GeneratedImage image) {
        try {
            // 注意：Gemini返回的是文本描述，不是实际图片URL
            // 这里我们保存描述文本而不是下载图片
            String fileName = "gemini_description_" + image.getTopicId() + "_" + System.currentTimeMillis() + ".txt";
            Path imagePath = Paths.get(imageDirectory, fileName);
            
            // 确保目录存在
            Files.createDirectories(imagePath.getParent());
            
            // 保存描述文本
            Files.write(imagePath, image.getOriginalUrl().getBytes());
            
            // 更新数据库中的文件信息
            try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
                GeneratedImageMapper mapper = session.getMapper(GeneratedImageMapper.class);
                
                image.setLocalPath(imagePath.toString());
                image.setFileName(fileName);
                image.setFileFormat("TXT");
                image.setFileSize((long) image.getOriginalUrl().getBytes().length);
                image.setStatus("DOWNLOADED");
                image.setDownloadedAt(LocalDateTime.now());
                
                mapper.update(image);
                session.commit();
                
                System.out.println("✅ 图片描述已保存: " + imagePath);
            }
            
        } catch (Exception e) {
            System.err.println("❌ 保存图片描述失败: " + e.getMessage());
            
            // 更新状态为失败
            try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
                GeneratedImageMapper mapper = session.getMapper(GeneratedImageMapper.class);
                image.setStatus("FAILED");
                image.setErrorMessage("保存失败: " + e.getMessage());
                mapper.update(image);
                session.commit();
            } catch (Exception dbError) {
                System.err.println("❌ 更新失败状态也失败了: " + dbError.getMessage());
            }
        }
    }
    
    /**
     * 获取存储使用统计
     */
    public StorageUsageStats getStorageUsage(int days) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            GeneratedImageMapper mapper = session.getMapper(GeneratedImageMapper.class);
            
            LocalDateTime startDate = LocalDateTime.now().minusDays(days);
            return mapper.getStorageUsageStats(startDate);
            
        } catch (Exception e) {
            System.err.println("❌ 获取存储统计失败: " + e.getMessage());
            return new StorageUsageStats(0L, 0L, 0L, 0L, 0.0);
        }
    }
    
    /**
     * 批量处理话题
     */
    public void processTopicsInBatch(List<TrendingTopic> topics) {
        System.out.println("🎨 开始批量处理 " + topics.size() + " 个话题（使用Gemini）...");
        
        int successCount = 0;
        int failureCount = 0;
        
        for (TrendingTopic topic : topics) {
            try {
                System.out.println("🔄 处理话题: " + topic.getTopic());
                
                GeneratedImageResult result = generateAndSaveImage(topic);
                
                if (result.isSuccess()) {
                    successCount++;
                    System.out.println("✅ 话题处理成功: " + topic.getTopic());
                    
                    // 更新话题状态
                    topicService.updateTopicStatus(topic.getId(), "COMPLETED");
                } else {
                    failureCount++;
                    System.err.println("❌ 话题处理失败: " + topic.getTopic() + " - " + result.getErrorMessage());
                    
                    // 更新话题状态
                    topicService.updateTopicStatus(topic.getId(), "FAILED");
                }
                
                // 添加延迟避免API限制
                Thread.sleep(2000);
                
            } catch (Exception e) {
                failureCount++;
                System.err.println("❌ 处理话题异常: " + topic.getTopic() + " - " + e.getMessage());
                
                try {
                    topicService.updateTopicStatus(topic.getId(), "FAILED");
                } catch (Exception updateError) {
                    System.err.println("❌ 更新话题状态失败: " + updateError.getMessage());
                }
            }
        }
        
        System.out.println("🎉 批量处理完成! 成功: " + successCount + ", 失败: " + failureCount);
    }
    
    /**
     * 图片生成结果类
     */
    @lombok.Data
    @lombok.AllArgsConstructor
    public static class GeneratedImageResult {
        private final GeneratedImage image;
        private final AiPrompt prompt;
        private final boolean success;
        private final String errorMessage;
    }
}
