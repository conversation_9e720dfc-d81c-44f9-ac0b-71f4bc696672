package com.hal.service.database;

import com.hal.database.DatabaseConfig;
import com.hal.entity.DailyTask;
import com.hal.entity.TaskStatistics;
import com.hal.mapper.DailyTaskMapper;
import org.apache.ibatis.session.SqlSession;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 每日任务业务服务类
 */
public class DailyTaskService {

    private final DatabaseConfig databaseConfig;

    public DailyTaskService() {
        this.databaseConfig = DatabaseConfig.getInstance();
    }

    /**
     * 创建新的每日任务记录
     */
    public DailyTask createTask(LocalDateTime executionDate) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            DailyTaskMapper mapper = session.getMapper(DailyTaskMapper.class);

            DailyTask task = new DailyTask(executionDate, "PENDING");
            mapper.insert(task);
            session.commit();

            System.out.println("✅ 创建每日任务记录: ID=" + task.getId());
            return task;
        } catch (Exception e) {
            System.err.println("❌ 创建每日任务失败: " + e.getMessage());
            throw new RuntimeException("Failed to create daily task", e);
        }
    }

    /**
     * 开始执行任务
     */
    public void startTask(Long taskId) {
        updateTaskStatus(taskId, "RUNNING", null);
    }

    /**
     * 完成任务
     */
    public void completeTask(Long taskId, int totalTrends, int processedTrends,
                           int successfulImages, int successfulListings,
                           double totalCost, long executionTimeMs) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            DailyTaskMapper mapper = session.getMapper(DailyTaskMapper.class);

            DailyTask task = mapper.findById(taskId);
            if (task != null) {
                task.setStatus("SUCCESS");
                task.setTotalTrends(totalTrends);
                task.setProcessedTrends(processedTrends);
                task.setSuccessfulImages(successfulImages);
                task.setSuccessfulListings(successfulListings);
                task.setTotalCost(totalCost);
                task.setExecutionTimeMs(executionTimeMs);
                task.setUpdatedAt(LocalDateTime.now());

                mapper.update(task);
                session.commit();

                System.out.println("✅ 任务完成: ID=" + taskId + ", 成本=$" + totalCost);
            }
        } catch (Exception e) {
            System.err.println("❌ 完成任务失败: " + e.getMessage());
            throw new RuntimeException("Failed to complete task", e);
        }
    }

    /**
     * 任务失败
     */
    public void failTask(Long taskId, String errorMessage, long executionTimeMs) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            DailyTaskMapper mapper = session.getMapper(DailyTaskMapper.class);

            DailyTask task = mapper.findById(taskId);
            if (task != null) {
                task.setStatus("FAILED");
                task.setErrorMessage(errorMessage);
                task.setExecutionTimeMs(executionTimeMs);
                task.setUpdatedAt(LocalDateTime.now());

                mapper.update(task);
                session.commit();

                System.out.println("❌ 任务失败: ID=" + taskId + ", 错误: " + errorMessage);
            }
        } catch (Exception e) {
            System.err.println("❌ 更新任务失败状态失败: " + e.getMessage());
            throw new RuntimeException("Failed to fail task", e);
        }
    }

    /**
     * 部分完成任务
     */
    public void partialCompleteTask(Long taskId, int totalTrends, int processedTrends,
                                  int successfulImages, int successfulListings,
                                  double totalCost, String errorMessage, long executionTimeMs) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            DailyTaskMapper mapper = session.getMapper(DailyTaskMapper.class);

            DailyTask task = mapper.findById(taskId);
            if (task != null) {
                task.setStatus("PARTIAL");
                task.setTotalTrends(totalTrends);
                task.setProcessedTrends(processedTrends);
                task.setSuccessfulImages(successfulImages);
                task.setSuccessfulListings(successfulListings);
                task.setTotalCost(totalCost);
                task.setErrorMessage(errorMessage);
                task.setExecutionTimeMs(executionTimeMs);
                task.setUpdatedAt(LocalDateTime.now());

                mapper.update(task);
                session.commit();

                System.out.println("⚠️ 任务部分完成: ID=" + taskId + ", 处理了 " + processedTrends + "/" + totalTrends);
            }
        } catch (Exception e) {
            System.err.println("❌ 更新任务部分完成状态失败: " + e.getMessage());
            throw new RuntimeException("Failed to partial complete task", e);
        }
    }

    /**
     * 更新任务状态
     */
    public void updateTaskStatus(Long taskId, String status, String errorMessage) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            DailyTaskMapper mapper = session.getMapper(DailyTaskMapper.class);

            DailyTask task = mapper.findById(taskId);
            if (task != null) {
                task.setStatus(status);
                task.setErrorMessage(errorMessage);
                task.setUpdatedAt(LocalDateTime.now());

                mapper.update(task);
                session.commit();
            }
        } catch (Exception e) {
            System.err.println("❌ 更新任务状态失败: " + e.getMessage());
            throw new RuntimeException("Failed to update task status", e);
        }
    }

    /**
     * 根据ID查询任务
     */
    public DailyTask getTaskById(Long taskId) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            DailyTaskMapper mapper = session.getMapper(DailyTaskMapper.class);
            return mapper.findById(taskId);
        } catch (Exception e) {
            System.err.println("❌ 查询任务失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 查询今日任务
     */
    public List<DailyTask> getTodayTasks() {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            DailyTaskMapper mapper = session.getMapper(DailyTaskMapper.class);
            return mapper.findByDate(LocalDate.now());
        } catch (Exception e) {
            System.err.println("❌ 查询今日任务失败: " + e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询最近的任务
     */
    public List<DailyTask> getRecentTasks(int limit) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            DailyTaskMapper mapper = session.getMapper(DailyTaskMapper.class);
            return mapper.findRecent(limit);
        } catch (Exception e) {
            System.err.println("❌ 查询最近任务失败: " + e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询指定状态的任务
     */
    public List<DailyTask> getTasksByStatus(String status) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            DailyTaskMapper mapper = session.getMapper(DailyTaskMapper.class);
            return mapper.findByStatus(status);
        } catch (Exception e) {
            System.err.println("❌ 查询指定状态任务失败: " + e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询指定日期范围的任务
     */
    public List<DailyTask> getTasksByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            DailyTaskMapper mapper = session.getMapper(DailyTaskMapper.class);
            return mapper.findByDateRange(startDate, endDate);
        } catch (Exception e) {
            System.err.println("❌ 查询日期范围任务失败: " + e.getMessage());
            return List.of();
        }
    }

    /**
     * 获取任务统计信息
     */
    public TaskStatistics getTaskStatistics(LocalDateTime startDate, LocalDateTime endDate) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            DailyTaskMapper mapper = session.getMapper(DailyTaskMapper.class);
            return mapper.getStatistics(startDate, endDate);
        } catch (Exception e) {
            System.err.println("❌ 获取任务统计失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 检查今日是否已有任务
     */
    public boolean hasTodayTask() {
        List<DailyTask> todayTasks = getTodayTasks();
        return !todayTasks.isEmpty();
    }

    /**
     * 获取今日最新任务
     */
    public DailyTask getTodayLatestTask() {
        List<DailyTask> todayTasks = getTodayTasks();
        return todayTasks.isEmpty() ? null : todayTasks.get(0);
    }

    /**
     * 删除旧任务记录
     */
    public int cleanupOldTasks(int retentionDays) {
        try (SqlSession session = databaseConfig.getSqlSessionFactory().openSession()) {
            DailyTaskMapper mapper = session.getMapper(DailyTaskMapper.class);
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(retentionDays);
            int deletedCount = mapper.deleteBeforeDate(cutoffDate);
            session.commit();

            System.out.println("🧹 清理了 " + deletedCount + " 条旧任务记录");
            return deletedCount;
        } catch (Exception e) {
            System.err.println("❌ 清理旧任务失败: " + e.getMessage());
            return 0;
        }
    }

    /**
     * 打印任务执行报告
     */
    public void printTaskReport(DailyTask task) {
        if (task == null) return;

        System.out.println("\n📋 任务执行报告");
        System.out.println("=".repeat(50));
        System.out.println("任务ID: " + task.getId());
        System.out.println("执行时间: " + task.getExecutionDate());
        System.out.println("状态: " + getStatusEmoji(task.getStatus()) + " " + task.getStatus());
        System.out.println("热搜总数: " + (task.getTotalTrends() != null ? task.getTotalTrends() : 0));
        System.out.println("处理数量: " + (task.getProcessedTrends() != null ? task.getProcessedTrends() : 0));
        System.out.println("成功图片: " + (task.getSuccessfulImages() != null ? task.getSuccessfulImages() : 0));
        System.out.println("成功上架: " + (task.getSuccessfulListings() != null ? task.getSuccessfulListings() : 0));
        System.out.println("总成本: $" + (task.getTotalCost() != null ? String.format("%.2f", task.getTotalCost()) : "0.00"));

        if (task.getExecutionTimeMs() != null) {
            System.out.println("执行时间: " + formatDuration(task.getExecutionTimeMs()));
        }

        if (task.getErrorMessage() != null && !task.getErrorMessage().isEmpty()) {
            System.out.println("错误信息: " + task.getErrorMessage());
        }

        System.out.println("=".repeat(50));
    }

    /**
     * 获取状态对应的表情符号
     */
    private String getStatusEmoji(String status) {
        switch (status) {
            case "SUCCESS":
                return "✅";
            case "FAILED":
                return "❌";
            case "PARTIAL":
                return "⚠️";
            case "RUNNING":
                return "🔄";
            case "PENDING":
                return "⏳";
            default:
                return "❓";
        }
    }

    /**
     * 格式化持续时间
     */
    private String formatDuration(long milliseconds) {
        long seconds = milliseconds / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;

        if (hours > 0) {
            return String.format("%d小时%d分钟%d秒", hours, minutes % 60, seconds % 60);
        } else if (minutes > 0) {
            return String.format("%d分钟%d秒", minutes, seconds % 60);
        } else {
            return String.format("%d秒", seconds);
        }
    }
}
