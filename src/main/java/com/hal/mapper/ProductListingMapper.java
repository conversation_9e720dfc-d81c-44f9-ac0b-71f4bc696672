package com.hal.mapper;

import com.hal.entity.ProductListing;
import com.hal.entity.ListingStats;
import com.hal.entity.SalesReport;
import com.hal.entity.InventoryStatus;
import org.apache.ibatis.annotations.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 商品上架数据访问接口
 */
@Mapper
public interface ProductListingMapper {

    /**
     * 插入新的商品上架记录
     */
    @Insert("""
        INSERT INTO product_listings (topic_id, image_id, amazon_listing_id, asin, title,
                                    description, keywords, price, currency, category, brand,
                                    status, marketplace, region, view_count, order_count,
                                    total_revenue, listed_at, created_at, updated_at)
        VALUES (#{topicId}, #{imageId}, #{amazonListingId}, #{asin}, #{title},
                #{description}, #{keywords}, #{price}, #{currency}, #{category}, #{brand},
                #{status}, #{marketplace}, #{region}, #{viewCount}, #{orderCount},
                #{totalRevenue}, #{listedAt}, #{createdAt}, #{updatedAt})
    """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(ProductListing productListing);

    /**
     * 更新商品状态
     */
    @Update("UPDATE product_listings SET status = #{status}, error_message = #{errorMessage}, updated_at = CURRENT_TIMESTAMP WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") String status, @Param("errorMessage") String errorMessage);

    /**
     * 更新Amazon信息
     */
    @Update("""
        UPDATE product_listings
        SET amazon_listing_id = #{amazonListingId}, asin = #{asin},
            status = 'LISTED', listed_at = #{listedAt}, updated_at = CURRENT_TIMESTAMP
        WHERE id = #{id}
    """)
    int updateAmazonInfo(@Param("id") Long id, @Param("amazonListingId") String amazonListingId,
                        @Param("asin") String asin, @Param("listedAt") LocalDateTime listedAt);

    /**
     * 更新销售数据
     */
    @Update("""
        UPDATE product_listings
        SET view_count = #{viewCount}, order_count = #{orderCount},
            total_revenue = #{totalRevenue}, last_sale_at = #{lastSaleAt}, updated_at = CURRENT_TIMESTAMP
        WHERE id = #{id}
    """)
    int updateSalesData(@Param("id") Long id, @Param("viewCount") Integer viewCount,
                       @Param("orderCount") Integer orderCount, @Param("totalRevenue") BigDecimal totalRevenue,
                       @Param("lastSaleAt") LocalDateTime lastSaleAt);

    /**
     * 根据ID查询商品
     */
    @Select("SELECT * FROM product_listings WHERE id = #{id}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "topicId", column = "topic_id"),
        @Result(property = "imageId", column = "image_id"),
        @Result(property = "amazonListingId", column = "amazon_listing_id"),
        @Result(property = "totalRevenue", column = "total_revenue"),
        @Result(property = "viewCount", column = "view_count"),
        @Result(property = "orderCount", column = "order_count"),
        @Result(property = "lastSaleAt", column = "last_sale_at"),
        @Result(property = "listedAt", column = "listed_at"),
        @Result(property = "createdAt", column = "created_at"),
        @Result(property = "updatedAt", column = "updated_at"),
        @Result(property = "errorMessage", column = "error_message")
    })
    ProductListing findById(Long id);

    /**
     * 根据话题ID查询商品
     */
    @Select("SELECT * FROM product_listings WHERE topic_id = #{topicId} ORDER BY created_at DESC")
    @ResultMap("productListingResultMap")
    List<ProductListing> findByTopicId(Long topicId);

    /**
     * 根据状态查询商品
     */
    @Select("SELECT * FROM product_listings WHERE status = #{status} ORDER BY created_at DESC LIMIT #{limit}")
    @ResultMap("productListingResultMap")
    List<ProductListing> findByStatus(@Param("status") String status, @Param("limit") int limit);

    /**
     * 根据ASIN查询商品
     */
    @Select("SELECT * FROM product_listings WHERE asin = #{asin}")
    @ResultMap("productListingResultMap")
    ProductListing findByAsin(String asin);

    /**
     * 根据Amazon Listing ID查询商品
     */
    @Select("SELECT * FROM product_listings WHERE amazon_listing_id = #{amazonListingId}")
    @ResultMap("productListingResultMap")
    ProductListing findByAmazonListingId(String amazonListingId);

    /**
     * 查询指定日期范围的商品
     */
    @Select("""
        SELECT * FROM product_listings
        WHERE created_at >= #{startDate} AND created_at <= #{endDate}
        ORDER BY created_at DESC
    """)
    @ResultMap("productListingResultMap")
    List<ProductListing> findByDateRange(@Param("startDate") LocalDateTime startDate,
                                        @Param("endDate") LocalDateTime endDate);

    /**
     * 查询指定价格范围的商品
     */
    @Select("""
        SELECT * FROM product_listings
        WHERE price >= #{minPrice} AND price <= #{maxPrice}
        ORDER BY price ASC
        LIMIT #{limit}
    """)
    @ResultMap("productListingResultMap")
    List<ProductListing> findByPriceRange(@Param("minPrice") BigDecimal minPrice,
                                         @Param("maxPrice") BigDecimal maxPrice,
                                         @Param("limit") int limit);

    /**
     * 搜索商品标题
     */
    @Select("""
        SELECT * FROM product_listings
        WHERE title ILIKE CONCAT('%', #{keyword}, '%')
        ORDER BY created_at DESC
        LIMIT #{limit}
    """)
    @ResultMap("productListingResultMap")
    List<ProductListing> searchByTitle(@Param("keyword") String keyword, @Param("limit") int limit);

    /**
     * 查询热销商品
     */
    @Select("""
        SELECT * FROM product_listings
        WHERE order_count > 0
        ORDER BY order_count DESC, total_revenue DESC
        LIMIT #{limit}
    """)
    @ResultMap("productListingResultMap")
    List<ProductListing> findBestSellers(@Param("limit") int limit);

    /**
     * 查询需要更新状态的商品
     */
    @Select("""
        SELECT * FROM product_listings
        WHERE status IN ('LISTED', 'ACTIVE')
        AND (last_sale_at IS NULL OR last_sale_at < #{checkDate})
        ORDER BY listed_at ASC
    """)
    @ResultMap("productListingResultMap")
    List<ProductListing> findProductsNeedingStatusUpdate(@Param("checkDate") LocalDateTime checkDate);

    /**
     * 统计商品上架情况
     */
    @Select("""
        SELECT
            status,
            marketplace,
            COUNT(*) as count,
            SUM(COALESCE(view_count, 0)) as total_views,
            SUM(COALESCE(order_count, 0)) as total_orders,
            SUM(COALESCE(total_revenue, 0)) as total_revenue,
            AVG(price) as avg_price
        FROM product_listings
        WHERE created_at >= #{startDate} AND created_at <= #{endDate}
        GROUP BY status, marketplace
        ORDER BY status, marketplace
    """)
    List<ListingStats> getListingStatistics(@Param("startDate") LocalDateTime startDate,
                                           @Param("endDate") LocalDateTime endDate);

    /**
     * 获取销售报告
     */
    @Select("SELECT DATE(listed_at) as date, COUNT(*) as listings_count, SUM(COALESCE(order_count, 0)) as total_orders, SUM(COALESCE(total_revenue, 0)) as total_revenue, AVG(price) as avg_price FROM product_listings WHERE listed_at >= #{startDate} AND listed_at <= #{endDate} GROUP BY DATE(listed_at) ORDER BY date DESC")
    List<SalesReport> getSalesReport(@Param("startDate") LocalDateTime startDate,
                                    @Param("endDate") LocalDateTime endDate);

    /**
     * 查询库存状态
     */
    @Select("""
        SELECT
            status,
            COUNT(*) as count,
            COUNT(CASE WHEN order_count > 0 THEN 1 END) as sold_count,
            COUNT(CASE WHEN view_count > 0 THEN 1 END) as viewed_count
        FROM product_listings
        GROUP BY status
        ORDER BY
            CASE status
                WHEN 'ACTIVE' THEN 1
                WHEN 'LISTED' THEN 2
                WHEN 'PENDING' THEN 3
                ELSE 4
            END
    """)
    List<InventoryStatus> getInventoryStatus();

    /**
     * 删除指定ID的商品记录
     */
    @Delete("DELETE FROM product_listings WHERE id = #{id}")
    int deleteById(Long id);

    /**
     * 删除指定日期之前的商品记录
     */
    @Delete("DELETE FROM product_listings WHERE created_at < #{date}")
    int deleteBeforeDate(LocalDateTime date);

}
