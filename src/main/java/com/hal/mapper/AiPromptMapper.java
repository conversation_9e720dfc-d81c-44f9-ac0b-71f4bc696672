package com.hal.mapper;

import com.hal.entity.AiPrompt;
import com.hal.entity.PromptUsageStats;
import com.hal.entity.PopularPromptTemplate;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * AI提示词数据访问接口
 */
@Mapper
public interface AiPromptMapper {

    /**
     * 插入新的AI提示词记录
     */
    @Insert("""
        INSERT INTO ai_prompts (topic_id, prompt_text, prompt_type, topic_type,
                               language, model, quality, style, size, token_count, created_at)
        VALUES (#{topicId}, #{promptText}, #{promptType}, #{topicType},
                #{language}, #{model}, #{quality}, #{style}, #{size}, #{tokenCount}, #{createdAt})
    """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(AiPrompt aiPrompt);

    /**
     * 根据ID查询提示词
     */
    @Select("SELECT * FROM ai_prompts WHERE id = #{id}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "topicId", column = "topic_id"),
        @Result(property = "promptText", column = "prompt_text"),
        @Result(property = "promptType", column = "prompt_type"),
        @Result(property = "topicType", column = "topic_type"),
        @Result(property = "tokenCount", column = "token_count"),
        @Result(property = "createdAt", column = "created_at")
    })
    AiPrompt findById(Long id);

    /**
     * 根据话题ID查询提示词
     */
    @Select("SELECT * FROM ai_prompts WHERE topic_id = #{topicId} ORDER BY created_at DESC")
    @ResultMap("aiPromptResultMap")
    List<AiPrompt> findByTopicId(Long topicId);

    /**
     * 根据提示词类型查询
     */
    @Select("SELECT * FROM ai_prompts WHERE prompt_type = #{promptType} ORDER BY created_at DESC LIMIT #{limit}")
    @ResultMap("aiPromptResultMap")
    List<AiPrompt> findByPromptType(@Param("promptType") String promptType, @Param("limit") int limit);

    /**
     * 根据话题类型查询提示词
     */
    @Select("SELECT * FROM ai_prompts WHERE topic_type = #{topicType} ORDER BY created_at DESC LIMIT #{limit}")
    @ResultMap("aiPromptResultMap")
    List<AiPrompt> findByTopicType(@Param("topicType") String topicType, @Param("limit") int limit);

    /**
     * 根据模型查询提示词
     */
    @Select("SELECT * FROM ai_prompts WHERE model = #{model} ORDER BY created_at DESC LIMIT #{limit}")
    @ResultMap("aiPromptResultMap")
    List<AiPrompt> findByModel(@Param("model") String model, @Param("limit") int limit);

    /**
     * 搜索提示词内容
     */
    @Select("SELECT * FROM ai_prompts WHERE prompt_text ILIKE CONCAT('%', #{keyword}, '%') ORDER BY created_at DESC LIMIT #{limit}")
    @ResultMap("aiPromptResultMap")
    List<AiPrompt> searchByContent(@Param("keyword") String keyword, @Param("limit") int limit);

    /**
     * 查询指定日期范围的提示词
     */
    @Select("""
        SELECT * FROM ai_prompts
        WHERE created_at >= #{startDate} AND created_at <= #{endDate}
        ORDER BY created_at DESC
    """)
    @ResultMap("aiPromptResultMap")
    List<AiPrompt> findByDateRange(@Param("startDate") LocalDateTime startDate,
                                  @Param("endDate") LocalDateTime endDate);

    /**
     * 统计提示词使用情况
     */
    @Select("""
        SELECT
            prompt_type,
            topic_type,
            model,
            COUNT(*) as usage_count,
            AVG(COALESCE(token_count, 0)) as avg_token_count,
            MAX(created_at) as last_used
        FROM ai_prompts
        WHERE created_at >= #{startDate} AND created_at <= #{endDate}
        GROUP BY prompt_type, topic_type, model
        ORDER BY usage_count DESC
    """)
    List<PromptUsageStats> getUsageStatistics(@Param("startDate") LocalDateTime startDate,
                                            @Param("endDate") LocalDateTime endDate);

    /**
     * 查找相似的提示词（基于文本相似度）
     */
    @Select("""
        SELECT *,
               similarity(prompt_text, #{promptText}) as similarity_score
        FROM ai_prompts
        WHERE similarity(prompt_text, #{promptText}) > #{threshold}
        ORDER BY similarity_score DESC
        LIMIT #{limit}
    """)
    @ResultMap("aiPromptResultMap")
    List<AiPrompt> findSimilarPrompts(@Param("promptText") String promptText,
                                     @Param("threshold") double threshold,
                                     @Param("limit") int limit);

    /**
     * 获取最受欢迎的提示词模板
     */
    @Select("""
        SELECT
            prompt_type,
            topic_type,
            COUNT(*) as usage_count,
            STRING_AGG(DISTINCT LEFT(prompt_text, 100), ' | ') as sample_prompts
        FROM ai_prompts
        WHERE created_at >= #{startDate}
        GROUP BY prompt_type, topic_type
        HAVING COUNT(*) >= #{minUsage}
        ORDER BY usage_count DESC
        LIMIT #{limit}
    """)
    List<PopularPromptTemplate> getPopularTemplates(@Param("startDate") LocalDateTime startDate,
                                                   @Param("minUsage") int minUsage,
                                                   @Param("limit") int limit);

    /**
     * 删除指定ID的提示词
     */
    @Delete("DELETE FROM ai_prompts WHERE id = #{id}")
    int deleteById(Long id);

    /**
     * 删除指定日期之前的提示词记录
     */
    @Delete("DELETE FROM ai_prompts WHERE created_at < #{date}")
    int deleteBeforeDate(LocalDateTime date);

}
