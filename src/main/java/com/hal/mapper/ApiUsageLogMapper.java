package com.hal.mapper;

import com.hal.entity.ApiUsageLog;
import com.hal.entity.ApiUsageStats;
import com.hal.entity.DailyApiStats;
import com.hal.entity.ErrorAnalysis;
import com.hal.entity.QuotaUsage;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * API使用日志数据访问接口
 */
@Mapper
public interface ApiUsageLogMapper {

    /**
     * 插入新的API使用日志
     */
    @Insert("""
        INSERT INTO api_usage_logs (task_id, api_provider, api_endpoint, request_method,
                                   request_params, response_status, http_status_code, response_message,
                                   response_time_ms, cost, currency, retry_count, error_code, error_message,
                                   daily_quota_used, daily_quota_limit, rate_limit_remaining, rate_limit_reset_at,
                                   request_at, response_at, created_at)
        VALUES (#{taskId}, #{apiProvider}, #{apiEndpoint}, #{requestMethod},
                #{requestParams}::jsonb, #{responseStatus}, #{httpStatusCode}, #{responseMessage},
                #{responseTimeMs}, #{cost}, #{currency}, #{retryCount}, #{errorCode}, #{errorMessage},
                #{dailyQuotaUsed}, #{dailyQuotaLimit}, #{rateLimitRemaining}, #{rateLimitResetAt},
                #{requestAt}, #{responseAt}, #{createdAt})
    """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(ApiUsageLog apiUsageLog);

    /**
     * 更新响应信息
     */
    @Update("""
        UPDATE api_usage_logs
        SET response_status = #{responseStatus}, http_status_code = #{httpStatusCode},
            response_message = #{responseMessage}, response_time_ms = #{responseTimeMs},
            error_code = #{errorCode}, error_message = #{errorMessage}, response_at = #{responseAt}
        WHERE id = #{id}
    """)
    int updateResponse(@Param("id") Long id, @Param("responseStatus") String responseStatus,
                      @Param("httpStatusCode") Integer httpStatusCode, @Param("responseMessage") String responseMessage,
                      @Param("responseTimeMs") Long responseTimeMs, @Param("errorCode") String errorCode,
                      @Param("errorMessage") String errorMessage, @Param("responseAt") LocalDateTime responseAt);

    /**
     * 更新重试次数
     */
    @Update("UPDATE api_usage_logs SET retry_count = #{retryCount} WHERE id = #{id}")
    int updateRetryCount(@Param("id") Long id, @Param("retryCount") Integer retryCount);

    /**
     * 根据ID查询日志
     */
    @Select("SELECT * FROM api_usage_logs WHERE id = #{id}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "taskId", column = "task_id"),
        @Result(property = "apiProvider", column = "api_provider"),
        @Result(property = "apiEndpoint", column = "api_endpoint"),
        @Result(property = "requestMethod", column = "request_method"),
        @Result(property = "requestParams", column = "request_params"),
        @Result(property = "responseStatus", column = "response_status"),
        @Result(property = "httpStatusCode", column = "http_status_code"),
        @Result(property = "responseMessage", column = "response_message"),
        @Result(property = "responseTimeMs", column = "response_time_ms"),
        @Result(property = "retryCount", column = "retry_count"),
        @Result(property = "errorCode", column = "error_code"),
        @Result(property = "errorMessage", column = "error_message"),
        @Result(property = "dailyQuotaUsed", column = "daily_quota_used"),
        @Result(property = "dailyQuotaLimit", column = "daily_quota_limit"),
        @Result(property = "rateLimitRemaining", column = "rate_limit_remaining"),
        @Result(property = "rateLimitResetAt", column = "rate_limit_reset_at"),
        @Result(property = "requestAt", column = "request_at"),
        @Result(property = "responseAt", column = "response_at"),
        @Result(property = "createdAt", column = "created_at")
    })
    ApiUsageLog findById(Long id);

    /**
     * 根据任务ID查询日志
     */
    @Select("SELECT * FROM api_usage_logs WHERE task_id = #{taskId} ORDER BY request_at ASC")
    @ResultMap("apiUsageLogResultMap")
    List<ApiUsageLog> findByTaskId(Long taskId);

    /**
     * 根据API提供商查询日志
     */
    @Select("SELECT * FROM api_usage_logs WHERE api_provider = #{apiProvider} ORDER BY request_at DESC LIMIT #{limit}")
    @ResultMap("apiUsageLogResultMap")
    List<ApiUsageLog> findByProvider(@Param("apiProvider") String apiProvider, @Param("limit") int limit);

    /**
     * 根据响应状态查询日志
     */
    @Select("SELECT * FROM api_usage_logs WHERE response_status = #{responseStatus} ORDER BY request_at DESC LIMIT #{limit}")
    @ResultMap("apiUsageLogResultMap")
    List<ApiUsageLog> findByResponseStatus(@Param("responseStatus") String responseStatus, @Param("limit") int limit);

    /**
     * 查询指定日期范围的日志
     */
    @Select("""
        SELECT * FROM api_usage_logs
        WHERE request_at >= #{startDate} AND request_at <= #{endDate}
        ORDER BY request_at DESC
    """)
    @ResultMap("apiUsageLogResultMap")
    List<ApiUsageLog> findByDateRange(@Param("startDate") LocalDateTime startDate,
                                     @Param("endDate") LocalDateTime endDate);

    /**
     * 查询慢请求日志
     */
    @Select("""
        SELECT * FROM api_usage_logs
        WHERE response_time_ms > #{thresholdMs}
        ORDER BY response_time_ms DESC
        LIMIT #{limit}
    """)
    @ResultMap("apiUsageLogResultMap")
    List<ApiUsageLog> findSlowRequests(@Param("thresholdMs") Long thresholdMs, @Param("limit") int limit);

    /**
     * 查询错误日志
     */
    @Select("""
        SELECT * FROM api_usage_logs
        WHERE response_status = 'FAILED'
        AND request_at >= #{startDate}
        ORDER BY request_at DESC
        LIMIT #{limit}
    """)
    @ResultMap("apiUsageLogResultMap")
    List<ApiUsageLog> findRecentErrors(@Param("startDate") LocalDateTime startDate, @Param("limit") int limit);

    /**
     * 统计API使用情况
     */
    @Select("""
        SELECT
            api_provider,
            response_status,
            COUNT(*) as request_count,
            SUM(COALESCE(cost, 0)) as total_cost,
            AVG(COALESCE(response_time_ms, 0)) as avg_response_time,
            MAX(response_time_ms) as max_response_time,
            SUM(retry_count) as total_retries
        FROM api_usage_logs
        WHERE request_at >= #{startDate} AND request_at <= #{endDate}
        GROUP BY api_provider, response_status
        ORDER BY api_provider, response_status
    """)
    List<ApiUsageStats> getUsageStatistics(@Param("startDate") LocalDateTime startDate,
                                          @Param("endDate") LocalDateTime endDate);

    /**
     * 获取每日API使用统计
     */
    @Select("""
        SELECT
            DATE(request_at) as date,
            api_provider,
            COUNT(*) as request_count,
            COUNT(CASE WHEN response_status = 'SUCCESS' THEN 1 END) as success_count,
            COUNT(CASE WHEN response_status = 'FAILED' THEN 1 END) as failure_count,
            SUM(COALESCE(cost, 0)) as total_cost,
            AVG(COALESCE(response_time_ms, 0)) as avg_response_time
        FROM api_usage_logs
        WHERE request_at >= #{startDate} AND request_at <= #{endDate}
        GROUP BY DATE(request_at), api_provider
        ORDER BY date DESC, api_provider
    """)
    List<DailyApiStats> getDailyStatistics(@Param("startDate") LocalDateTime startDate,
                                          @Param("endDate") LocalDateTime endDate);

    /**
     * 获取错误分析
     */
    @Select("SELECT api_provider, error_code, COUNT(*) as error_count, STRING_AGG(DISTINCT error_message, ' | ') as sample_messages, MAX(request_at) as last_occurrence FROM api_usage_logs WHERE response_status = 'FAILED' AND request_at >= #{startDate} GROUP BY api_provider, error_code ORDER BY error_count DESC")
    List<ErrorAnalysis> getErrorAnalysis(@Param("startDate") LocalDateTime startDate);

    /**
     * 获取配额使用情况
     */
    @Select("SELECT api_provider, MAX(daily_quota_used) as current_quota_used, MAX(daily_quota_limit) as quota_limit, MAX(request_at) as last_update FROM api_usage_logs WHERE DATE(request_at) = CURRENT_DATE AND daily_quota_used IS NOT NULL GROUP BY api_provider")
    List<QuotaUsage> getCurrentQuotaUsage();

    /**
     * 删除指定ID的日志
     */
    @Delete("DELETE FROM api_usage_logs WHERE id = #{id}")
    int deleteById(Long id);

    /**
     * 删除指定日期之前的日志
     */
    @Delete("DELETE FROM api_usage_logs WHERE request_at < #{date}")
    int deleteBeforeDate(LocalDateTime date);

    /**
     * 清理旧的成功日志（保留错误日志更长时间）
     */
    @Delete("""
        DELETE FROM api_usage_logs
        WHERE response_status = 'SUCCESS'
        AND request_at < #{date}
    """)
    int cleanupSuccessLogs(LocalDateTime date);

}
