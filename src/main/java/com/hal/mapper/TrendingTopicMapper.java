package com.hal.mapper;

import com.hal.entity.TrendingTopic;
import com.hal.entity.TopicFrequency;
import com.hal.entity.TopicTypeStats;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 热搜话题数据访问接口
 */
@Mapper
public interface TrendingTopicMapper {

    /**
     * 插入新的热搜话题
     */
    @Insert("""
        INSERT INTO trending_topics (task_id, topic, topic_type, ranking, source,
                                   language, status, discovered_at, created_at)
        VALUES (#{taskId}, #{topic}, #{topicType}, #{ranking}, #{source},
                #{language}, #{status}, #{discoveredAt}, #{createdAt})
    """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(TrendingTopic trendingTopic);

    /**
     * 更新话题状态
     */
    @Update("UPDATE trending_topics SET status = #{status} WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 更新话题类型
     */
    @Update("UPDATE trending_topics SET topic_type = #{topicType} WHERE id = #{id}")
    int updateTopicType(@Param("id") Long id, @Param("topicType") String topicType);

    /**
     * 根据ID查询话题
     */
    @Select("SELECT * FROM trending_topics WHERE id = #{id}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "taskId", column = "task_id"),
        @Result(property = "topicType", column = "topic_type"),
        @Result(property = "discoveredAt", column = "discovered_at"),
        @Result(property = "createdAt", column = "created_at")
    })
    TrendingTopic findById(Long id);

    /**
     * 根据任务ID查询话题列表
     */
    @Select("SELECT * FROM trending_topics WHERE task_id = #{taskId} ORDER BY ranking ASC")
    @ResultMap("trendingTopicResultMap")
    List<TrendingTopic> findByTaskId(Long taskId);

    /**
     * 查询指定日期的热搜话题
     */
    @Select("SELECT * FROM trending_topics WHERE DATE(discovered_at) = DATE(#{date}) ORDER BY ranking ASC")
    @ResultMap("trendingTopicResultMap")
    List<TrendingTopic> findByDate(LocalDateTime date);

    /**
     * 查询指定话题类型的记录
     */
    @Select("SELECT * FROM trending_topics WHERE topic_type = #{topicType} ORDER BY discovered_at DESC LIMIT #{limit}")
    @ResultMap("trendingTopicResultMap")
    List<TrendingTopic> findByTopicType(@Param("topicType") String topicType, @Param("limit") int limit);

    /**
     * 查询指定状态的话题
     */
    @Select("SELECT * FROM trending_topics WHERE status = #{status} ORDER BY discovered_at DESC")
    @ResultMap("trendingTopicResultMap")
    List<TrendingTopic> findByStatus(String status);

    /**
     * 查询最近的话题
     */
    @Select("SELECT * FROM trending_topics ORDER BY discovered_at DESC LIMIT #{limit}")
    @ResultMap("trendingTopicResultMap")
    List<TrendingTopic> findRecent(@Param("limit") int limit);

    /**
     * 搜索话题（模糊匹配）
     */
    @Select("SELECT * FROM trending_topics WHERE topic ILIKE CONCAT('%', #{keyword}, '%') ORDER BY discovered_at DESC LIMIT #{limit}")
    @ResultMap("trendingTopicResultMap")
    List<TrendingTopic> searchByKeyword(@Param("keyword") String keyword, @Param("limit") int limit);

    /**
     * 查询热门话题（按出现频次）
     */
    @Select("""
        SELECT topic, COUNT(*) as frequency, MAX(discovered_at) as last_seen
        FROM trending_topics
        WHERE discovered_at >= #{startDate}
        GROUP BY topic
        HAVING COUNT(*) >= #{minFrequency}
        ORDER BY frequency DESC, last_seen DESC
        LIMIT #{limit}
    """)
    List<TopicFrequency> findPopularTopics(@Param("startDate") LocalDateTime startDate,
                                         @Param("minFrequency") int minFrequency,
                                         @Param("limit") int limit);

    /**
     * 统计话题类型分布
     */
    @Select("""
        SELECT
            topic_type,
            COUNT(*) as count,
            COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed_count
        FROM trending_topics
        WHERE discovered_at >= #{startDate} AND discovered_at <= #{endDate}
        GROUP BY topic_type
        ORDER BY count DESC
    """)
    List<TopicTypeStats> getTopicTypeStatistics(@Param("startDate") LocalDateTime startDate,
                                              @Param("endDate") LocalDateTime endDate);

    /**
     * 检查话题是否已存在（去重）
     */
    @Select("""
        SELECT COUNT(*) FROM trending_topics
        WHERE topic = #{topic} AND DATE(discovered_at) = DATE(#{date})
    """)
    int countByTopicAndDate(@Param("topic") String topic, @Param("date") LocalDateTime date);

    /**
     * 删除指定ID的话题
     */
    @Delete("DELETE FROM trending_topics WHERE id = #{id}")
    int deleteById(Long id);

    /**
     * 删除指定日期之前的话题记录
     */
    @Delete("DELETE FROM trending_topics WHERE discovered_at < #{date}")
    int deleteBeforeDate(LocalDateTime date);

}
