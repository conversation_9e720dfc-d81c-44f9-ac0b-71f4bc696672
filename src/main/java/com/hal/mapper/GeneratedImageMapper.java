package com.hal.mapper;

import com.hal.entity.GeneratedImage;
import com.hal.entity.ImageGenerationStats;
import com.hal.entity.DuplicateImage;
import com.hal.entity.StorageUsageStats;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 生成图片数据访问接口
 */
@Mapper
public interface GeneratedImageMapper {

    /**
     * 插入新的生成图片记录
     */
    @Insert("""
        INSERT INTO generated_images (topic_id, prompt_id, original_url, local_path,
                                    file_name, file_format, file_size, dimensions, quality,
                                    status, generation_cost, checksum, s3_url, cdn_url,
                                    generated_at, downloaded_at, created_at)
        VALUES (#{topicId}, #{promptId}, #{originalUrl}, #{localPath},
                #{fileName}, #{fileFormat}, #{fileSize}, #{dimensions}, #{quality},
                #{status}, #{generationCost}, #{checksum}, #{s3Url}, #{cdnUrl},
                #{generatedAt}, #{downloadedAt}, #{createdAt})
    """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(GeneratedImage generatedImage);

    /**
     * 更新图片状态
     */
    @Update("UPDATE generated_images SET status = #{status}, error_message = #{errorMessage} WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") String status, @Param("errorMessage") String errorMessage);

    /**
     * 更新下载信息
     */
    @Update("""
        UPDATE generated_images
        SET local_path = #{localPath}, file_name = #{fileName}, file_size = #{fileSize},
            checksum = #{checksum}, downloaded_at = #{downloadedAt}, status = 'DOWNLOADED'
        WHERE id = #{id}
    """)
    int updateDownloadInfo(@Param("id") Long id, @Param("localPath") String localPath,
                          @Param("fileName") String fileName, @Param("fileSize") Long fileSize,
                          @Param("checksum") String checksum, @Param("downloadedAt") LocalDateTime downloadedAt);

    /**
     * 更新云存储信息
     */
    @Update("UPDATE generated_images SET s3_url = #{s3Url}, cdn_url = #{cdnUrl}, status = 'UPLOADED' WHERE id = #{id}")
    int updateCloudUrls(@Param("id") Long id, @Param("s3Url") String s3Url, @Param("cdnUrl") String cdnUrl);

    /**
     * 根据ID查询图片
     */
    @Select("SELECT * FROM generated_images WHERE id = #{id}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "topicId", column = "topic_id"),
        @Result(property = "promptId", column = "prompt_id"),
        @Result(property = "originalUrl", column = "original_url"),
        @Result(property = "localPath", column = "local_path"),
        @Result(property = "fileName", column = "file_name"),
        @Result(property = "fileFormat", column = "file_format"),
        @Result(property = "fileSize", column = "file_size"),
        @Result(property = "generationCost", column = "generation_cost"),
        @Result(property = "errorMessage", column = "error_message"),
        @Result(property = "s3Url", column = "s3_url"),
        @Result(property = "cdnUrl", column = "cdn_url"),
        @Result(property = "generatedAt", column = "generated_at"),
        @Result(property = "downloadedAt", column = "downloaded_at"),
        @Result(property = "createdAt", column = "created_at")
    })
    GeneratedImage findById(Long id);

    /**
     * 根据话题ID查询图片
     */
    @Select("SELECT * FROM generated_images WHERE topic_id = #{topicId} ORDER BY created_at DESC")
    @ResultMap("generatedImageResultMap")
    List<GeneratedImage> findByTopicId(Long topicId);

    /**
     * 根据状态查询图片
     */
    @Select("SELECT * FROM generated_images WHERE status = #{status} ORDER BY created_at DESC LIMIT #{limit}")
    @ResultMap("generatedImageResultMap")
    List<GeneratedImage> findByStatus(@Param("status") String status, @Param("limit") int limit);

    /**
     * 查询指定日期范围的图片
     */
    @Select("""
        SELECT * FROM generated_images
        WHERE generated_at >= #{startDate} AND generated_at <= #{endDate}
        ORDER BY generated_at DESC
    """)
    @ResultMap("generatedImageResultMap")
    List<GeneratedImage> findByDateRange(@Param("startDate") LocalDateTime startDate,
                                        @Param("endDate") LocalDateTime endDate);

    /**
     * 查询指定质量的图片
     */
    @Select("SELECT * FROM generated_images WHERE quality = #{quality} ORDER BY created_at DESC LIMIT #{limit}")
    @ResultMap("generatedImageResultMap")
    List<GeneratedImage> findByQuality(@Param("quality") String quality, @Param("limit") int limit);

    /**
     * 查询需要下载的图片
     */
    @Select("SELECT * FROM generated_images WHERE status = 'GENERATED' AND local_path IS NULL ORDER BY created_at ASC")
    @ResultMap("generatedImageResultMap")
    List<GeneratedImage> findPendingDownloads();

    /**
     * 查询需要上传到云端的图片
     */
    @Select("SELECT * FROM generated_images WHERE status = 'DOWNLOADED' AND s3_url IS NULL ORDER BY created_at ASC")
    @ResultMap("generatedImageResultMap")
    List<GeneratedImage> findPendingUploads();

    /**
     * 统计图片生成情况
     */
    @Select("""
        SELECT
            quality,
            status,
            COUNT(*) as count,
            SUM(COALESCE(generation_cost, 0)) as total_cost,
            AVG(COALESCE(file_size, 0)) as avg_file_size
        FROM generated_images
        WHERE generated_at >= #{startDate} AND generated_at <= #{endDate}
        GROUP BY quality, status
        ORDER BY quality, status
    """)
    List<ImageGenerationStats> getGenerationStatistics(@Param("startDate") LocalDateTime startDate,
                                                       @Param("endDate") LocalDateTime endDate);

    /**
     * 查找重复的图片（基于校验和）
     */
    @Select("SELECT checksum, COUNT(*) as duplicate_count, STRING_AGG(id::text, ',') as image_ids FROM generated_images WHERE checksum IS NOT NULL GROUP BY checksum HAVING COUNT(*) > 1 ORDER BY duplicate_count DESC")
    List<DuplicateImage> findDuplicateImages();

    /**
     * 查询存储使用情况
     */
    @Select("""
        SELECT
            COUNT(*) as total_images,
            SUM(COALESCE(file_size, 0)) as total_size,
            COUNT(CASE WHEN local_path IS NOT NULL THEN 1 END) as local_stored,
            COUNT(CASE WHEN s3_url IS NOT NULL THEN 1 END) as cloud_stored,
            AVG(COALESCE(file_size, 0)) as avg_file_size
        FROM generated_images
        WHERE generated_at >= #{startDate}
    """)
    StorageUsageStats getStorageUsage(@Param("startDate") LocalDateTime startDate);

    /**
     * 删除指定ID的图片记录
     */
    @Delete("DELETE FROM generated_images WHERE id = #{id}")
    int deleteById(Long id);

    /**
     * 删除指定日期之前的图片记录
     */
    @Delete("DELETE FROM generated_images WHERE generated_at < #{date}")
    int deleteBeforeDate(LocalDateTime date);

    /**
     * 清理失败的图片记录
     */
    @Delete("DELETE FROM generated_images WHERE status = 'FAILED' AND created_at < #{date}")
    int cleanupFailedImages(LocalDateTime date);

}
