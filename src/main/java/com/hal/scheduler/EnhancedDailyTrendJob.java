package com.hal.scheduler;

import com.hal.service.AmazonListingService;
import com.hal.service.OpenAIImageService;
import com.hal.service.TwitterTrendService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 增强版每日热搜T恤生成任务
 * 集成了成本控制、重试机制和完整的商品信息生成
 */
public class EnhancedDailyTrendJob implements Job {

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        System.out.println("🚀 开始执行增强版每日热搜T恤生成任务");
        System.out.println("执行时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println("=".repeat(60));

        try {
            // 1. 初始化服务
            TwitterTrendService twitterService = new TwitterTrendService();
            OpenAIImageService imageService = new OpenAIImageService();
            AmazonListingService amazonService = new AmazonListingService();

            // 设置代理（如果需要）
            twitterService.setProxy("127.0.0.1", 7890);
            imageService.setProxy("127.0.0.1", 7890);
            amazonService.setProxy("127.0.0.1", 7890);

            // 显示成本控制状态
            System.out.println("\n📊 当前使用状态:");
            imageService.getCostControl().printUsageStats();

            // 2. 获取推特热搜
            System.out.println("\n📱 正在获取推特热搜...");
            List<String> trends = twitterService.getTrendingTopics();

            if (trends.isEmpty()) {
                System.out.println("❌ 未获取到热搜数据，任务终止");
                return;
            }

            // 3. 智能处理热搜（基于成本控制）
            System.out.println("\n🎨 开始生成T恤设计...");
            List<AmazonListingService.ProductInfo> products = new ArrayList<>();
            
            // 根据当前使用情况动态调整处理数量
            int maxTrends = calculateMaxTrends(imageService, trends.size());
            System.out.println("📊 将处理前 " + maxTrends + " 个热搜（共 " + trends.size() + " 个可用）");

            int successCount = 0;
            int failureCount = 0;

            for (int i = 0; i < maxTrends; i++) {
                String trend = trends.get(i);
                System.out.println("\n🔄 处理热搜 " + (i + 1) + "/" + maxTrends + ": " + trend);

                try {
                    // 检查成本控制
                    if (!imageService.getCostControl().canMakeOpenAIRequest(false)) {
                        System.err.println("⚠️ 已达到每日限制，停止处理更多热搜");
                        break;
                    }

                    // 使用新的集成方法生成完整商品信息
                    AmazonListingService.ProductInfo product = imageService.generateCompleteProductInfo(trend, false);
                    products.add(product);

                    System.out.println("✅ 热搜 '" + trend + "' 的T恤设计生成完成");
                    successCount++;
                    
                    // 显示当前使用统计
                    if (i % 2 == 0) { // 每处理2个显示一次统计
                        imageService.getCostControl().printUsageStats();
                    }

                    // 智能延迟：根据成本使用情况调整
                    int delay = calculateDelay(imageService);
                    System.out.println("⏱️ 等待 " + delay + "ms 后继续...");
                    Thread.sleep(delay);

                } catch (Exception e) {
                    System.err.println("❌ 处理热搜 '" + trend + "' 时出错: " + e.getMessage());
                    failureCount++;
                    
                    // 如果连续失败太多，提前结束
                    if (failureCount >= 3 && successCount == 0) {
                        System.err.println("⚠️ 连续失败过多，提前结束任务");
                        break;
                    }
                    
                    continue;
                }
            }

            // 4. 批量上架到亚马逊
            if (!products.isEmpty()) {
                System.out.println("\n🛒 开始上架商品到亚马逊...");
                System.out.println("准备上架 " + products.size() + " 个商品");
                
                int uploadedCount = 0;
                for (AmazonListingService.ProductInfo product : products) {
                    try {
                        String listingId = amazonService.listProduct(product);
                        if (listingId != null) {
                            uploadedCount++;
                            System.out.println("✅ 商品上架成功: " + product.getTitle());
                        }
                        
                        // 添加延迟避免Amazon API限制
                        Thread.sleep(2000);
                        
                    } catch (Exception e) {
                        System.err.println("❌ 商品上架失败: " + product.getTitle() + " - " + e.getMessage());
                    }
                }
                
                System.out.println("\n📈 上架结果: " + uploadedCount + "/" + products.size() + " 个商品成功上架");
            } else {
                System.out.println("\n⚠️ 没有生成任何商品，跳过上架步骤");
            }

            // 5. 任务总结
            printTaskSummary(successCount, failureCount, products.size(), imageService);

        } catch (Exception e) {
            System.err.println("❌ 任务执行失败: " + e.getMessage());
            e.printStackTrace();
            throw new JobExecutionException(e);
        }

        System.out.println("\n🎯 每日热搜T恤生成任务完成");
        System.out.println("=".repeat(60));
    }

    /**
     * 根据当前使用情况计算最大处理数量
     */
    private int calculateMaxTrends(OpenAIImageService imageService, int availableTrends) {
        var stats = imageService.getCostControl().getTodayUsageStats();
        
        // 基于剩余配额计算
        int remainingRequests = stats.maxOpenAIRequests - stats.openAIRequests;
        double remainingBudget = stats.maxDailyBudget - stats.dailyCost;
        
        // 每个请求大约0.04美元（标准质量）
        int budgetBasedLimit = (int) (remainingBudget / 0.04);
        
        // 取最小值，但至少处理1个（如果有配额的话）
        int maxByQuota = Math.min(remainingRequests, budgetBasedLimit);
        int maxTrends = Math.min(Math.min(maxByQuota, availableTrends), 5); // 最多5个
        
        return Math.max(maxTrends, 0);
    }

    /**
     * 根据使用情况计算延迟时间
     */
    private int calculateDelay(OpenAIImageService imageService) {
        var stats = imageService.getCostControl().getTodayUsageStats();
        
        // 如果接近限制，增加延迟
        if (stats.getCostUsagePercentage() > 80) {
            return 8000; // 8秒
        } else if (stats.getCostUsagePercentage() > 60) {
            return 6000; // 6秒
        } else if (stats.getCostUsagePercentage() > 40) {
            return 4000; // 4秒
        } else {
            return 3000; // 3秒
        }
    }

    /**
     * 打印任务总结
     */
    private void printTaskSummary(int successCount, int failureCount, int uploadedCount, OpenAIImageService imageService) {
        System.out.println("\n📋 任务执行总结");
        System.out.println("-".repeat(40));
        System.out.println("✅ 成功生成设计: " + successCount + " 个");
        System.out.println("❌ 生成失败: " + failureCount + " 个");
        System.out.println("🛒 成功上架: " + uploadedCount + " 个");
        
        var stats = imageService.getCostControl().getTodayUsageStats();
        System.out.println("💰 今日成本: $" + String.format("%.2f", stats.dailyCost) + "/$" + String.format("%.2f", stats.maxDailyBudget));
        System.out.println("📊 API使用: " + stats.openAIRequests + "/" + stats.maxOpenAIRequests + " 次");
        
        // 预测信息
        if (stats.openAIRequests > 0) {
            double avgCostPerRequest = stats.dailyCost / stats.openAIRequests;
            int remainingRequests = (int) ((stats.maxDailyBudget - stats.dailyCost) / avgCostPerRequest);
            System.out.println("🔮 预计还可处理: " + Math.max(0, remainingRequests) + " 个热搜");
        }
        
        System.out.println("-".repeat(40));
    }
}
