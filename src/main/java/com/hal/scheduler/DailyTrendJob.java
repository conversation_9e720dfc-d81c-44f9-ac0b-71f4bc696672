package com.hal.scheduler;

import com.hal.service.AmazonListingService;
import com.hal.service.OpenAIImageService;
import com.hal.service.TwitterTrendService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 每日热搜T恤生成任务
 * 这个任务会：
 * 1. 获取推特热搜前10条
 * 2. 为每个热搜生成讽刺搞笑的T恤图片
 * 3. 将生成的T恤上架到亚马逊
 */
public class DailyTrendJob implements Job {

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        System.out.println("=".repeat(60));
        System.out.println("开始执行每日热搜T恤生成任务");
        System.out.println("执行时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println("=".repeat(60));

        try {
            // 1. 初始化服务
            TwitterTrendService twitterService = new TwitterTrendService();
            OpenAIImageService imageService = new OpenAIImageService();
            AmazonListingService amazonService = new AmazonListingService();

            // 设置代理（如果需要）
            twitterService.setProxy("127.0.0.1", 7890);
            imageService.setProxy("127.0.0.1", 7890);
            amazonService.setProxy("127.0.0.1", 7890);

            // 显示成本控制状态
            imageService.getCostControl().printUsageStats();

            // 2. 获取推特热搜
            System.out.println("\n📱 正在获取推特热搜...");
            List<String> trends = twitterService.getTrendingTopics();

            if (trends.isEmpty()) {
                System.out.println("❌ 未获取到热搜数据，任务终止");
                return;
            }

            // 3. 为每个热搜生成图片和商品信息
            System.out.println("\n🎨 开始生成T恤设计...");
            List<AmazonListingService.ProductInfo> products = new ArrayList<>();

            for (int i = 0; i < trends.size(); i++) {
                String trend = trends.get(i);
                System.out.println("\n处理热搜 " + (i + 1) + "/" + trends.size() + ": " + trend);

                try {
                    // 生成图片
                    String imageUrl = imageService.generateTShirtImage(trend);

                    // 下载图片到本地
                    String fileName = "trend_" + (i + 1) + "_" +
                                    LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                    String localImagePath = imageService.downloadImage(imageUrl, fileName);

                    // 创建商品信息
                    AmazonListingService.ProductInfo product =
                        new AmazonListingService.ProductInfo(trend, imageUrl, localImagePath);
                    products.add(product);

                    System.out.println("✓ 热搜 '" + trend + "' 的T恤设计生成完成");

                    // 添加延迟避免API限制
                    Thread.sleep(3000);

                } catch (Exception e) {
                    System.err.println("❌ 处理热搜 '" + trend + "' 时出错: " + e.getMessage());
                    continue;
                }
            }

            // 4. 批量上架到亚马逊
            if (!products.isEmpty()) {
                System.out.println("\n🛒 开始上架商品到亚马逊...");
                amazonService.batchListProducts(products);
            } else {
                System.out.println("\n❌ 没有成功生成的商品，跳过上架步骤");
            }

            // 5. 任务完成总结
            System.out.println("\n" + "=".repeat(60));
            System.out.println("📊 任务执行完成总结:");
            System.out.println("- 获取热搜数量: " + trends.size());
            System.out.println("- 成功生成商品: " + products.size());
            System.out.println("- 完成时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            System.out.println("=".repeat(60));

        } catch (Exception e) {
            System.err.println("❌ 任务执行失败: " + e.getMessage());
            e.printStackTrace();
            throw new JobExecutionException(e);
        }
    }
}
