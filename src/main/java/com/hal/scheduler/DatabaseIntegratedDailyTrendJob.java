package com.hal.scheduler;

import com.hal.entity.DailyTask;
import com.hal.entity.TrendingTopic;
import com.hal.service.*;
import com.hal.service.database.*;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 集成数据库的增强版每日热搜T恤生成任务
 * 完整记录任务执行过程和结果
 */
public class DatabaseIntegratedDailyTrendJob implements Job {
    
    private final DailyTaskService taskService;
    private final TrendingTopicService topicService;
    private final DatabaseIntegratedOpenAIService openAIService;
    private final ApiUsageLogService apiLogService;
    private final CostControlService costControl;
    private final TwitterTrendService twitterService;
    private final AmazonListingService amazonService;
    private final ProductInfoGenerator productGenerator;
    
    public DatabaseIntegratedDailyTrendJob() {
        this.taskService = new DailyTaskService();
        this.topicService = new TrendingTopicService();
        this.openAIService = new DatabaseIntegratedOpenAIService();
        this.apiLogService = new ApiUsageLogService();
        this.costControl = new CostControlService();
        this.twitterService = new TwitterTrendService();
        this.amazonService = new AmazonListingService();
        this.productGenerator = new ProductInfoGenerator();
        
        // 设置代理
        twitterService.setProxy("127.0.0.1", 7890);
        openAIService.setProxy("127.0.0.1", 7890);
        amazonService.setProxy("127.0.0.1", 7890);
    }
    
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        long startTime = System.currentTimeMillis();
        DailyTask task = null;
        
        try {
            System.out.println("\n🚀 开始执行每日热搜T恤生成任务");
            System.out.println("执行时间: " + LocalDateTime.now());
            System.out.println("=".repeat(60));
            
            // 1. 创建任务记录
            task = taskService.createTask(LocalDateTime.now());
            taskService.startTask(task.getId());
            
            // 2. 检查成本控制
            if (!costControl.canMakeOpenAIRequest(false)) {
                String errorMsg = "已达到每日OpenAI请求限制或预算限制";
                taskService.failTask(task.getId(), errorMsg, System.currentTimeMillis() - startTime);
                System.err.println("❌ " + errorMsg);
                return;
            }
            
            // 3. 获取热搜话题
            List<String> trends = fetchTrendingTopics(task.getId());
            if (trends.isEmpty()) {
                String errorMsg = "未获取到热搜话题";
                taskService.failTask(task.getId(), errorMsg, System.currentTimeMillis() - startTime);
                System.err.println("❌ " + errorMsg);
                return;
            }
            
            // 4. 保存热搜话题到数据库
            List<TrendingTopic> savedTopics = topicService.saveTrendingTopics(task.getId(), trends, "Twitter");
            
            // 5. 处理每个话题
            TaskExecutionResult result = processTopics(task.getId(), savedTopics);
            
            // 6. 完成任务记录
            long executionTime = System.currentTimeMillis() - startTime;
            
            if (result.hasAnySuccess()) {
                if (result.hasAnyFailure()) {
                    taskService.partialCompleteTask(
                        task.getId(),
                        result.totalTopics,
                        result.processedTopics,
                        result.successfulImages,
                        result.successfulListings,
                        result.totalCost,
                        result.getErrorSummary(),
                        executionTime
                    );
                } else {
                    taskService.completeTask(
                        task.getId(),
                        result.totalTopics,
                        result.processedTopics,
                        result.successfulImages,
                        result.successfulListings,
                        result.totalCost,
                        executionTime
                    );
                }
            } else {
                taskService.failTask(task.getId(), result.getErrorSummary(), executionTime);
            }
            
            // 7. 打印执行报告
            printExecutionReport(task.getId(), result, executionTime);
            
            // 8. 清理旧数据（可选）
            performMaintenanceTasks();
            
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            String errorMsg = "任务执行异常: " + e.getMessage();
            
            if (task != null) {
                taskService.failTask(task.getId(), errorMsg, executionTime);
            }
            
            System.err.println("❌ " + errorMsg);
            e.printStackTrace();
            throw new JobExecutionException(e);
        }
    }
    
    /**
     * 获取热搜话题
     */
    private List<String> fetchTrendingTopics(Long taskId) {
        var apiLog = apiLogService.logRequestStart(taskId, "Twitter", "/2/trends/by/woeid", "GET", "woeid=1");
        
        try {
            long startTime = System.currentTimeMillis();
            List<String> trends = twitterService.getTrendingTopics();
            long responseTime = System.currentTimeMillis() - startTime;
            
            apiLogService.logRequestComplete(apiLog.getId(), "SUCCESS", 200, 
                "获取到 " + trends.size() + " 个热搜话题", responseTime, 0.0);
            
            System.out.println("✅ 获取到 " + trends.size() + " 个热搜话题");
            return trends;
            
        } catch (Exception e) {
            long responseTime = System.currentTimeMillis() - System.currentTimeMillis();
            apiLogService.logRequestFailed(apiLog.getId(), "FETCH_ERROR", e.getMessage(), responseTime);
            
            System.err.println("❌ 获取热搜失败: " + e.getMessage());
            return List.of();
        }
    }
    
    /**
     * 处理话题列表
     */
    private TaskExecutionResult processTopics(Long taskId, List<TrendingTopic> topics) {
        TaskExecutionResult result = new TaskExecutionResult();
        result.totalTopics = topics.size();
        
        // 根据成本控制限制处理数量
        int maxTopicsToProcess = costControl.getMaxTopicsCanProcess();
        int topicsToProcess = Math.min(topics.size(), maxTopicsToProcess);
        
        System.out.println("📝 计划处理 " + topicsToProcess + "/" + topics.size() + " 个话题");
        
        for (int i = 0; i < topicsToProcess; i++) {
            TrendingTopic topic = topics.get(i);
            
            try {
                System.out.println("\n🔄 处理话题 " + (i + 1) + "/" + topicsToProcess + ": " + topic.getTopic());
                
                // 检查是否还能继续处理
                if (!costControl.canMakeOpenAIRequest(false)) {
                    System.out.println("⚠️ 已达到成本限制，停止处理剩余话题");
                    break;
                }
                
                // 处理单个话题
                TopicProcessingResult topicResult = processSingleTopic(taskId, topic);
                result.addTopicResult(topicResult);
                
                // 添加延迟避免API限流
                Thread.sleep(3000);
                
            } catch (Exception e) {
                System.err.println("❌ 处理话题失败: " + topic.getTopic() + " - " + e.getMessage());
                result.addError("话题 '" + topic.getTopic() + "' 处理失败: " + e.getMessage());
            }
        }
        
        return result;
    }
    
    /**
     * 处理单个话题
     */
    private TopicProcessingResult processSingleTopic(Long taskId, TrendingTopic topic) {
        TopicProcessingResult result = new TopicProcessingResult(topic.getTopic());
        
        try {
            // 1. 生成图片
            var imageResult = openAIService.generateTShirtImageWithRecord(topic.getId(), topic.getTopic(), false);
            
            if (imageResult.isSuccess()) {
                result.imageGenerated = true;
                result.imageCost = imageResult.getImage().getGenerationCost();
                
                // 2. 生成商品信息
                var productInfo = productGenerator.generateProductInfo(
                    topic.getTopic(),
                    imageResult.getImage().getOriginalUrl(),
                    imageResult.getImage().getLocalPath()
                );
                
                // 3. 上架商品
                var listingLog = apiLogService.logRequestStart(taskId, "Amazon", "/listings/2021-08-01/items", "PUT", 
                    "sku=" + topic.getId());
                
                try {
                    long startTime = System.currentTimeMillis();
                    String listingId = amazonService.listProduct(productInfo);
                    long responseTime = System.currentTimeMillis() - startTime;
                    
                    if (listingId != null && !listingId.isEmpty()) {
                        result.productListed = true;
                        apiLogService.logRequestComplete(listingLog.getId(), "SUCCESS", 200, 
                            "商品上架成功: " + listingId, responseTime, 0.0);
                        System.out.println("✅ 商品上架成功: " + listingId);
                    } else {
                        apiLogService.logRequestComplete(listingLog.getId(), "FAILED", 400, 
                            "商品上架失败", responseTime, 0.0);
                        result.addError("商品上架失败");
                    }
                    
                } catch (Exception e) {
                    apiLogService.logRequestFailed(listingLog.getId(), "LISTING_ERROR", e.getMessage(), 0L);
                    result.addError("商品上架异常: " + e.getMessage());
                }
                
            } else {
                result.addError("图片生成失败: " + imageResult.getErrorMessage());
            }
            
        } catch (Exception e) {
            result.addError("话题处理异常: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 打印执行报告
     */
    private void printExecutionReport(Long taskId, TaskExecutionResult result, long executionTime) {
        System.out.println("\n📋 任务执行报告");
        System.out.println("=".repeat(60));
        System.out.println("任务ID: " + taskId);
        System.out.println("执行时间: " + formatDuration(executionTime));
        System.out.println("话题总数: " + result.totalTopics);
        System.out.println("处理数量: " + result.processedTopics);
        System.out.println("成功图片: " + result.successfulImages);
        System.out.println("成功上架: " + result.successfulListings);
        System.out.println("总成本: $" + String.format("%.4f", result.totalCost));
        
        if (!result.errors.isEmpty()) {
            System.out.println("\n❌ 错误信息:");
            result.errors.forEach(error -> System.out.println("  - " + error));
        }
        
        // 打印成本控制状态
        costControl.printUsageStats();
        
        // 打印API使用报告
        apiLogService.printUsageReport(1);
        
        System.out.println("=".repeat(60));
    }
    
    /**
     * 执行维护任务
     */
    private void performMaintenanceTasks() {
        try {
            System.out.println("\n🧹 执行维护任务...");
            
            // 清理旧数据（保留90天）
            int retentionDays = 90;
            
            int deletedTasks = taskService.cleanupOldTasks(retentionDays);
            int deletedTopics = topicService.cleanupOldTopics(retentionDays);
            int deletedImages = openAIService.cleanupFailedImages(7); // 失败图片只保留7天
            int deletedLogs = apiLogService.cleanupOldLogs(30); // 日志保留30天
            
            if (deletedTasks + deletedTopics + deletedImages + deletedLogs > 0) {
                System.out.println("✅ 维护完成: 清理了 " + 
                    (deletedTasks + deletedTopics + deletedImages + deletedLogs) + " 条记录");
            }
            
        } catch (Exception e) {
            System.err.println("⚠️ 维护任务执行失败: " + e.getMessage());
        }
    }
    
    /**
     * 格式化持续时间
     */
    private String formatDuration(long milliseconds) {
        long seconds = milliseconds / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        
        if (hours > 0) {
            return String.format("%d小时%d分钟%d秒", hours, minutes % 60, seconds % 60);
        } else if (minutes > 0) {
            return String.format("%d分钟%d秒", minutes, seconds % 60);
        } else {
            return String.format("%d秒", seconds);
        }
    }
    
    /**
     * 任务执行结果类
     */
    private static class TaskExecutionResult {
        int totalTopics = 0;
        int processedTopics = 0;
        int successfulImages = 0;
        int successfulListings = 0;
        double totalCost = 0.0;
        List<String> errors = new java.util.ArrayList<>();
        
        void addTopicResult(TopicProcessingResult topicResult) {
            processedTopics++;
            if (topicResult.imageGenerated) {
                successfulImages++;
                totalCost += topicResult.imageCost != null ? topicResult.imageCost : 0.0;
            }
            if (topicResult.productListed) {
                successfulListings++;
            }
            errors.addAll(topicResult.errors);
        }
        
        void addError(String error) {
            errors.add(error);
        }
        
        boolean hasAnySuccess() {
            return successfulImages > 0 || successfulListings > 0;
        }
        
        boolean hasAnyFailure() {
            return !errors.isEmpty() || successfulImages < processedTopics;
        }
        
        String getErrorSummary() {
            if (errors.isEmpty()) return null;
            return String.join("; ", errors.stream().limit(3).toList()) + 
                   (errors.size() > 3 ? "... (+" + (errors.size() - 3) + " more)" : "");
        }
    }
    
    /**
     * 话题处理结果类
     */
    private static class TopicProcessingResult {
        String topic;
        boolean imageGenerated = false;
        boolean productListed = false;
        Double imageCost;
        List<String> errors = new java.util.ArrayList<>();
        
        TopicProcessingResult(String topic) {
            this.topic = topic;
        }
        
        void addError(String error) {
            errors.add(error);
        }
    }
}
