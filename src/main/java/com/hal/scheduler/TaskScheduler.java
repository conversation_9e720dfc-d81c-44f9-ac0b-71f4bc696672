package com.hal.scheduler;

import org.quartz.*;
import org.quartz.impl.StdSchedulerFactory;
import org.quartz.impl.matchers.GroupMatcher;

/**
 * 任务调度器
 * 负责管理定时任务的启动、停止和调度
 */
public class TaskScheduler {
    private Scheduler scheduler;

    public TaskScheduler() {
        // 创建调度器
        SchedulerFactory schedulerFactory = new StdSchedulerFactory();
        try {
            this.scheduler = schedulerFactory.getScheduler();
        } catch (SchedulerException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 启动调度器并设置每日任务
     */
    public void start() throws SchedulerException {
        // 创建任务详情
        JobDetail job = JobBuilder.newJob(DailyTrendJob.class)
                .withIdentity("dailyTrendJob", "trendGroup")
                .withDescription("每日热搜T恤生成任务")
                .build();

        // 创建触发器 - 每天上午9点执行
        Trigger trigger = TriggerBuilder.newTrigger()
                .withIdentity("dailyTrendTrigger", "trendGroup")
                .withDescription("每日上午9点触发")
                .withSchedule(CronScheduleBuilder.cronSchedule("0 0 9 * * ?")) // 每天9:00
                .build();

        // 调度任务
        scheduler.scheduleJob(job, trigger);

        // 启动调度器
        scheduler.start();

        System.out.println("✅ 任务调度器已启动");
        System.out.println("📅 每日任务将在上午9:00执行");
        System.out.println("🔄 下次执行时间: " + trigger.getNextFireTime());
    }

    /**
     * 立即执行一次任务（用于测试）
     */
    public void executeNow() throws SchedulerException {
        JobDetail job = JobBuilder.newJob(DailyTrendJob.class)
                .withIdentity("immediateJob", "testGroup")
                .build();

        Trigger trigger = TriggerBuilder.newTrigger()
                .withIdentity("immediateTrigger", "testGroup")
                .startNow()
                .build();

        scheduler.scheduleJob(job, trigger);

        System.out.println("🚀 立即执行任务已触发");
    }

    /**
     * 设置自定义执行时间
     */
    public void scheduleCustomTime(String cronExpression, String description) throws SchedulerException {
        JobDetail job = JobBuilder.newJob(DailyTrendJob.class)
                .withIdentity("customJob", "customGroup")
                .withDescription(description)
                .build();

        Trigger trigger = TriggerBuilder.newTrigger()
                .withIdentity("customTrigger", "customGroup")
                .withDescription(description)
                .withSchedule(CronScheduleBuilder.cronSchedule(cronExpression))
                .build();

        scheduler.scheduleJob(job, trigger);

        System.out.println("✅ 自定义任务已设置: " + description);
        System.out.println("🔄 下次执行时间: " + trigger.getNextFireTime());
    }

    /**
     * 停止调度器
     */
    public void shutdown() throws SchedulerException {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown(true);
            System.out.println("🛑 任务调度器已停止");
        }
    }

    /**
     * 获取调度器状态
     */
    public void printStatus() throws SchedulerException {
        System.out.println("\n📊 调度器状态:");
        System.out.println("- 是否启动: " + scheduler.isStarted());
        System.out.println("- 是否关闭: " + scheduler.isShutdown());
        System.out.println("- 任务数量: " + scheduler.getJobKeys(GroupMatcher.anyGroup()).size());

        // 显示所有已调度的任务
        for (JobKey jobKey : scheduler.getJobKeys(GroupMatcher.anyGroup())) {
            JobDetail jobDetail = scheduler.getJobDetail(jobKey);
            System.out.println("- 任务: " + jobKey.getName() + " (" + jobDetail.getDescription() + ")");

            for (Trigger trigger : scheduler.getTriggersOfJob(jobKey)) {
                System.out.println("  触发器: " + trigger.getKey().getName());
                System.out.println("  下次执行: " + trigger.getNextFireTime());
            }
        }
    }

    /**
     * 暂停任务
     */
    public void pauseJob(String jobName, String groupName) throws SchedulerException {
        JobKey jobKey = JobKey.jobKey(jobName, groupName);
        scheduler.pauseJob(jobKey);
        System.out.println("⏸️ 任务已暂停: " + jobName);
    }

    /**
     * 恢复任务
     */
    public void resumeJob(String jobName, String groupName) throws SchedulerException {
        JobKey jobKey = JobKey.jobKey(jobName, groupName);
        scheduler.resumeJob(jobKey);
        System.out.println("▶️ 任务已恢复: " + jobName);
    }

    /**
     * 删除任务
     */
    public void deleteJob(String jobName, String groupName) throws SchedulerException {
        JobKey jobKey = JobKey.jobKey(jobName, groupName);
        scheduler.deleteJob(jobKey);
        System.out.println("🗑️ 任务已删除: " + jobName);
    }

    /**
     * 调度增强版每日任务
     */
    public void scheduleEnhancedDailyJob(String time) throws SchedulerException {
        // 解析时间
        String[] timeParts = time.split(":");
        int hour = Integer.parseInt(timeParts[0]);
        int minute = Integer.parseInt(timeParts[1]);

        // 先删除现有任务（如果存在）
        try {
            deleteJob("enhancedDailyTrendJob", "trendGroup");
        } catch (Exception e) {
            // 忽略删除错误
        }

        // 创建增强版任务详情
        JobDetail job = JobBuilder.newJob(EnhancedDailyTrendJob.class)
                .withIdentity("enhancedDailyTrendJob", "trendGroup")
                .withDescription("增强版每日热搜的T恤生成任务")
                .build();

        // 创建触发器
        String cronExpression = String.format("0 %d %d * * ?", minute, hour);
        Trigger trigger = TriggerBuilder.newTrigger()
                .withIdentity("enhancedDailyTrendTrigger", "trendGroup")
                .withDescription("每日" + time + "触发")
                .withSchedule(CronScheduleBuilder.cronSchedule(cronExpression))
                .build();

        // 调度任务
        scheduler.scheduleJob(job, trigger);

        // 启动调度器
        if (!scheduler.isStarted()) {
            scheduler.start();
        }

        System.out.println("✅ 增强版任务调度器已启动");
        System.out.println("📅 每日任务将在" + time + "执行");
        System.out.println("🔄 下次执行时间: " + trigger.getNextFireTime());
    }

    /**
     * 启动调度器
     */
    public void startScheduler() throws SchedulerException {
        if (!scheduler.isStarted()) {
            scheduler.start();
            System.out.println("✅ 调度器已启动");
        }
    }

    /**
     * 停止调度器
     */
    public void stopScheduler() throws SchedulerException {
        if (scheduler != null && scheduler.isStarted()) {
            scheduler.shutdown();
            System.out.println("✅ 任务调度器已停止");
        }
    }

    /**
     * 检查调度器是否运行
     */
    public boolean isRunning() throws SchedulerException {
        return scheduler.isStarted() && !scheduler.isShutdown();
    }
}
