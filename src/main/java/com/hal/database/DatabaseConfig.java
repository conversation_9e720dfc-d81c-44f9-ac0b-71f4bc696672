package com.hal.database;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.flywaydb.core.Flyway;

import javax.sql.DataSource;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Properties;

/**
 * 数据库配置管理类
 */
public class DatabaseConfig {
    
    private static final String DB_CONFIG_FILE = "database.properties";
    private static final String MYBATIS_CONFIG_FILE = "mybatis-config.xml";
    
    private static DatabaseConfig instance;
    private DataSource dataSource;
    private SqlSessionFactory sqlSessionFactory;
    
    public DatabaseConfig() {
        initializeDataSource();
        initializeMyBatis();
        runMigrations();
    }
    
    public static synchronized DatabaseConfig getInstance() {
        if (instance == null) {
            instance = new DatabaseConfig();
        }
        return instance;
    }
    
    /**
     * 初始化数据源
     */
    private void initializeDataSource() {
        try {
            Properties dbProps = loadDatabaseProperties();
            
            HikariConfig config = new HikariConfig();
            config.setJdbcUrl(dbProps.getProperty("database.url", "*********************************************"));
            config.setUsername(dbProps.getProperty("database.username", "postgres"));
            config.setPassword(dbProps.getProperty("database.password", "password"));
            config.setDriverClassName("org.postgresql.Driver");
            
            // 连接池配置
            config.setMaximumPoolSize(Integer.parseInt(dbProps.getProperty("database.pool.max-size", "10")));
            config.setMinimumIdle(Integer.parseInt(dbProps.getProperty("database.pool.min-idle", "2")));
            config.setConnectionTimeout(Long.parseLong(dbProps.getProperty("database.pool.connection-timeout", "30000")));
            config.setIdleTimeout(Long.parseLong(dbProps.getProperty("database.pool.idle-timeout", "600000")));
            config.setMaxLifetime(Long.parseLong(dbProps.getProperty("database.pool.max-lifetime", "1800000")));
            
            // 连接验证
            config.setConnectionTestQuery("SELECT 1");
            config.setValidationTimeout(5000);
            
            this.dataSource = new HikariDataSource(config);
            
            System.out.println("✅ 数据库连接池初始化成功");
            
        } catch (Exception e) {
            System.err.println("❌ 数据库连接池初始化失败: " + e.getMessage());
            throw new RuntimeException("Failed to initialize database connection pool", e);
        }
    }
    
    /**
     * 初始化MyBatis
     */
    private void initializeMyBatis() {
        try {
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream(MYBATIS_CONFIG_FILE);
            if (inputStream == null) {
                // 如果没有配置文件，使用默认配置
                this.sqlSessionFactory = createDefaultSqlSessionFactory();
            } else {
                this.sqlSessionFactory = new SqlSessionFactoryBuilder().build(inputStream);
            }
            
            System.out.println("✅ MyBatis初始化成功");
            
        } catch (Exception e) {
            System.err.println("❌ MyBatis初始化失败: " + e.getMessage());
            throw new RuntimeException("Failed to initialize MyBatis", e);
        }
    }
    
    /**
     * 创建默认的SqlSessionFactory
     */
    private SqlSessionFactory createDefaultSqlSessionFactory() {
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.setEnvironment(new org.apache.ibatis.mapping.Environment(
            "development",
            new org.apache.ibatis.transaction.jdbc.JdbcTransactionFactory(),
            dataSource
        ));
        
        // 注册类型别名
        configuration.getTypeAliasRegistry().registerAlias("DailyTask", com.hal.entity.DailyTask.class);
        configuration.getTypeAliasRegistry().registerAlias("TrendingTopic", com.hal.entity.TrendingTopic.class);
        configuration.getTypeAliasRegistry().registerAlias("AiPrompt", com.hal.entity.AiPrompt.class);
        configuration.getTypeAliasRegistry().registerAlias("GeneratedImage", com.hal.entity.GeneratedImage.class);
        configuration.getTypeAliasRegistry().registerAlias("ProductListing", com.hal.entity.ProductListing.class);
        configuration.getTypeAliasRegistry().registerAlias("ApiUsageLog", com.hal.entity.ApiUsageLog.class);
        
        // 配置设置
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.setUseGeneratedKeys(true);
        configuration.setDefaultExecutorType(org.apache.ibatis.session.ExecutorType.REUSE);
        
        return new org.apache.ibatis.session.SqlSessionFactoryBuilder().build(configuration);
    }
    
    /**
     * 运行数据库迁移
     */
    private void runMigrations() {
        try {
            Flyway flyway = Flyway.configure()
                .dataSource(dataSource)
                .locations("classpath:db/migration")
                .baselineOnMigrate(true)
                .validateOnMigrate(true)
                .load();
            
            flyway.migrate();
            System.out.println("✅ 数据库迁移完成");
            
        } catch (Exception e) {
            System.err.println("❌ 数据库迁移失败: " + e.getMessage());
            // 不抛出异常，允许系统继续运行
        }
    }
    
    /**
     * 加载数据库配置
     */
    private Properties loadDatabaseProperties() {
        Properties props = new Properties();
        
        try {
            // 首先尝试从类路径加载
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream(DB_CONFIG_FILE);
            if (inputStream != null) {
                props.load(inputStream);
                inputStream.close();
                System.out.println("✅ 从类路径加载数据库配置: " + DB_CONFIG_FILE);
                return props;
            }
            
            // 然后尝试从当前目录加载
            if (Files.exists(Paths.get(DB_CONFIG_FILE))) {
                props.load(Files.newInputStream(Paths.get(DB_CONFIG_FILE)));
                System.out.println("✅ 从当前目录加载数据库配置: " + DB_CONFIG_FILE);
                return props;
            }
            
            System.out.println("⚠️ 未找到数据库配置文件，使用默认配置");
            
        } catch (IOException e) {
            System.err.println("⚠️ 加载数据库配置失败，使用默认配置: " + e.getMessage());
        }
        
        // 返回默认配置
        setDefaultProperties(props);
        return props;
    }
    
    /**
     * 设置默认数据库配置
     */
    private void setDefaultProperties(Properties props) {
        props.setProperty("database.url", "*********************************************");
        props.setProperty("database.username", "postgres");
        props.setProperty("database.password", "password");
        props.setProperty("database.pool.max-size", "10");
        props.setProperty("database.pool.min-idle", "2");
        props.setProperty("database.pool.connection-timeout", "30000");
        props.setProperty("database.pool.idle-timeout", "600000");
        props.setProperty("database.pool.max-lifetime", "1800000");
    }
    
    /**
     * 获取数据源
     */
    public DataSource getDataSource() {
        return dataSource;
    }
    
    /**
     * 获取SqlSessionFactory
     */
    public SqlSessionFactory getSqlSessionFactory() {
        return sqlSessionFactory;
    }
    
    /**
     * 测试数据库连接
     */
    public boolean testConnection() {
        try (var connection = dataSource.getConnection()) {
            return connection.isValid(5);
        } catch (Exception e) {
            System.err.println("❌ 数据库连接测试失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 关闭数据源
     */
    public void close() {
        if (dataSource instanceof HikariDataSource) {
            ((HikariDataSource) dataSource).close();
            System.out.println("✅ 数据库连接池已关闭");
        }
    }
}
