package com.hal.controller;

import com.hal.entity.TrendingTopic;
import com.hal.entity.TopicFrequency;
import com.hal.entity.TopicTypeStats;
import com.hal.service.database.TrendingTopicService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 热搜话题Controller
 */
@RestController
@RequestMapping("/api/topics")
@Tag(name = "热搜话题", description = "热搜话题的查询和分析")
public class TopicController {

    @Autowired
    private TrendingTopicService topicService;

    /**
     * 获取话题列表
     */
    @GetMapping
    @Operation(summary = "获取话题列表", description = "分页查询热搜话题")
    public ResponseEntity<PageInfo<TrendingTopic>> getTopics(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "20") int pageSize,
            @Parameter(description = "任务ID") @RequestParam(required = false) Long taskId,
            @Parameter(description = "话题类型") @RequestParam(required = false) String topicType,
            @Parameter(description = "状态") @RequestParam(required = false) String status,
            @Parameter(description = "来源") @RequestParam(required = false) String source,
            @Parameter(description = "语言") @RequestParam(required = false) String language,
            @Parameter(description = "关键词搜索") @RequestParam(required = false) String keyword,
            @Parameter(description = "开始日期") @RequestParam(required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {

        PageHelper.startPage(pageNum, pageSize);

        List<TrendingTopic> topics;

        if (taskId != null) {
            topics = topicService.getTopicsByTaskId(taskId);
        } else if (topicType != null) {
            topics = topicService.getTopicsByType(topicType, 1000);
        } else if (status != null) {
            topics = topicService.getTopicsByStatus(status);
        } else if (keyword != null) {
            topics = topicService.searchTopics(keyword, 1000);
        } else {
            // 默认查询最近的话题
            topics = topicService.getTopicsByStatus(""); // 获取所有状态
        }

        PageInfo<TrendingTopic> pageInfo = new PageInfo<>(topics);
        return ResponseEntity.ok(pageInfo);
    }

    /**
     * 获取话题详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取话题详情", description = "根据ID获取话题的详细信息")
    public ResponseEntity<TrendingTopic> getTopic(
            @Parameter(description = "话题ID") @PathVariable Long id) {

        TrendingTopic topic = topicService.getTopicById(id);
        if (topic == null) {
            return ResponseEntity.notFound().build();
        }

        return ResponseEntity.ok(topic);
    }

    /**
     * 搜索话题
     */
    @GetMapping("/search")
    @Operation(summary = "搜索话题", description = "根据关键词搜索话题")
    public ResponseEntity<List<TrendingTopic>> searchTopics(
            @Parameter(description = "搜索关键词") @RequestParam String keyword,
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "50") int limit) {

        List<TrendingTopic> topics = topicService.searchTopics(keyword, limit);
        return ResponseEntity.ok(topics);
    }

    /**
     * 获取热门话题
     */
    @GetMapping("/popular")
    @Operation(summary = "获取热门话题", description = "获取最受欢迎的话题")
    public ResponseEntity<List<TopicFrequency>> getPopularTopics(
            @Parameter(description = "天数") @RequestParam(defaultValue = "7") int days,
            @Parameter(description = "最小频次") @RequestParam(defaultValue = "2") int minFrequency,
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "20") int limit) {

        List<TopicFrequency> popularTopics =
            topicService.getPopularTopics(days, minFrequency, limit);

        return ResponseEntity.ok(popularTopics);
    }

    /**
     * 获取话题类型统计
     */
    @GetMapping("/type-statistics")
    @Operation(summary = "获取话题类型统计", description = "获取各类型话题的统计信息")
    public ResponseEntity<List<TopicTypeStats>> getTopicTypeStatistics(
            @Parameter(description = "开始日期") @RequestParam(required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {

        // 默认查询最近30天
        if (startDate == null) {
            startDate = LocalDate.now().minusDays(30);
        }
        if (endDate == null) {
            endDate = LocalDate.now();
        }

        List<TopicTypeStats> stats =
            topicService.getTopicTypeStatistics(
                startDate.atStartOfDay(),
                endDate.atTime(23, 59, 59)
            );

        return ResponseEntity.ok(stats);
    }

    /**
     * 更新话题状态
     */
    @PutMapping("/{id}/status")
    @Operation(summary = "更新话题状态", description = "更新指定话题的处理状态")
    public ResponseEntity<Map<String, Object>> updateTopicStatus(
            @Parameter(description = "话题ID") @PathVariable Long id,
            @Parameter(description = "新状态") @RequestParam String status) {

        Map<String, Object> result = new HashMap<>();

        try {
            TrendingTopic topic = topicService.getTopicById(id);
            if (topic == null) {
                result.put("success", false);
                result.put("message", "话题不存在");
                return ResponseEntity.notFound().build();
            }

            // 验证状态值
            String[] validStatuses = {"PENDING", "PROCESSING", "COMPLETED", "FAILED", "SKIPPED"};
            boolean isValidStatus = false;
            for (String validStatus : validStatuses) {
                if (validStatus.equals(status)) {
                    isValidStatus = true;
                    break;
                }
            }

            if (!isValidStatus) {
                result.put("success", false);
                result.put("message", "无效的状态值");
                return ResponseEntity.badRequest().body(result);
            }

            topicService.updateTopicStatus(id, status);

            result.put("success", true);
            result.put("message", "状态更新成功");
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 更新话题类型
     */
    @PutMapping("/{id}/type")
    @Operation(summary = "更新话题类型", description = "更新指定话题的分类类型")
    public ResponseEntity<Map<String, Object>> updateTopicType(
            @Parameter(description = "话题ID") @PathVariable Long id,
            @Parameter(description = "新类型") @RequestParam String topicType) {

        Map<String, Object> result = new HashMap<>();

        try {
            TrendingTopic topic = topicService.getTopicById(id);
            if (topic == null) {
                result.put("success", false);
                result.put("message", "话题不存在");
                return ResponseEntity.notFound().build();
            }

            // 验证类型值
            String[] validTypes = {"technology", "environment", "politics", "finance", "entertainment", "sports", "general"};
            boolean isValidType = false;
            for (String validType : validTypes) {
                if (validType.equals(topicType)) {
                    isValidType = true;
                    break;
                }
            }

            if (!isValidType) {
                result.put("success", false);
                result.put("message", "无效的类型值");
                return ResponseEntity.badRequest().body(result);
            }

            topicService.updateTopicType(id, topicType);

            result.put("success", true);
            result.put("message", "类型更新成功");
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 获取话题处理进度
     */
    @GetMapping("/progress")
    @Operation(summary = "获取话题处理进度", description = "获取话题的整体处理进度统计")
    public ResponseEntity<Map<String, Object>> getTopicProgress(
            @Parameter(description = "任务ID") @RequestParam(required = false) Long taskId,
            @Parameter(description = "天数") @RequestParam(defaultValue = "1") int days) {

        // TODO: 实现进度查询逻辑
        Map<String, Object> progress = new HashMap<>();
        progress.put("total", 0);
        progress.put("pending", 0);
        progress.put("processing", 0);
        progress.put("completed", 0);
        progress.put("failed", 0);
        progress.put("skipped", 0);

        return ResponseEntity.ok(progress);
    }

    /**
     * 获取话题趋势分析
     */
    @GetMapping("/trends")
    @Operation(summary = "获取话题趋势分析", description = "获取话题的趋势变化数据")
    public ResponseEntity<List<Map<String, Object>>> getTopicTrends(
            @Parameter(description = "天数") @RequestParam(defaultValue = "7") int days) {

        // TODO: 实现趋势分析逻辑
        return ResponseEntity.ok(List.of());
    }

    /**
     * 获取话题词云数据
     */
    @GetMapping("/wordcloud")
    @Operation(summary = "获取话题词云数据", description = "获取用于生成词云的话题关键词数据")
    public ResponseEntity<List<Map<String, Object>>> getTopicWordCloud(
            @Parameter(description = "天数") @RequestParam(defaultValue = "7") int days,
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "100") int limit) {

        // TODO: 实现词云数据生成逻辑
        return ResponseEntity.ok(List.of());
    }
}
