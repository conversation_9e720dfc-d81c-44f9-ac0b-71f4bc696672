package com.hal.controller;

import com.hal.entity.DailyTask;
import com.hal.service.database.DailyTaskService;
import com.hal.scheduler.DatabaseIntegratedDailyTrendJob;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务管理Controller
 */
@RestController
@RequestMapping("/api/tasks")
@Tag(name = "任务管理", description = "每日任务的创建、查询和管理")
public class TaskController {

    @Autowired
    private DailyTaskService taskService;

    /**
     * 立即执行任务
     */
    @PostMapping("/execute")
    @Operation(summary = "立即执行任务", description = "手动触发热搜T恤生成任务")
    public ResponseEntity<Map<String, Object>> executeTask() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查是否已有今日任务在运行
            DailyTask todayTask = taskService.getTodayLatestTask();
            if (todayTask != null && "RUNNING".equals(todayTask.getStatus())) {
                result.put("success", false);
                result.put("message", "今日已有任务正在执行中");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 异步执行任务
            new Thread(() -> {
                try {
                    DatabaseIntegratedDailyTrendJob job = new DatabaseIntegratedDailyTrendJob();
                    job.execute(null);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }).start();
            
            result.put("success", true);
            result.put("message", "任务已开始执行");
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "任务执行失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 获取任务列表
     */
    @GetMapping
    @Operation(summary = "获取任务列表", description = "分页查询任务执行记录")
    public ResponseEntity<PageInfo<DailyTask>> getTasks(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "10") int pageSize,
            @Parameter(description = "任务状态") @RequestParam(required = false) String status,
            @Parameter(description = "开始日期") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        
        PageHelper.startPage(pageNum, pageSize);
        
        List<DailyTask> tasks;
        if (status != null) {
            tasks = taskService.getTasksByStatus(status);
        } else if (startDate != null && endDate != null) {
            tasks = taskService.getTasksByDateRange(
                startDate.atStartOfDay(), 
                endDate.atTime(23, 59, 59)
            );
        } else {
            tasks = taskService.getRecentTasks(100); // 最近100条
        }
        
        PageInfo<DailyTask> pageInfo = new PageInfo<>(tasks);
        return ResponseEntity.ok(pageInfo);
    }

    /**
     * 获取任务详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取任务详情", description = "根据ID获取任务的详细信息")
    public ResponseEntity<DailyTask> getTask(
            @Parameter(description = "任务ID") @PathVariable Long id) {
        
        DailyTask task = taskService.getTaskById(id);
        if (task == null) {
            return ResponseEntity.notFound().build();
        }
        
        return ResponseEntity.ok(task);
    }

    /**
     * 获取今日任务
     */
    @GetMapping("/today")
    @Operation(summary = "获取今日任务", description = "获取今天的任务执行情况")
    public ResponseEntity<List<DailyTask>> getTodayTasks() {
        List<DailyTask> tasks = taskService.getTodayTasks();
        return ResponseEntity.ok(tasks);
    }

    /**
     * 获取任务统计
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取任务统计", description = "获取指定时间范围内的任务统计信息")
    public ResponseEntity<Map<String, Object>> getTaskStatistics(
            @Parameter(description = "开始日期") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        
        // 默认查询最近30天
        if (startDate == null) {
            startDate = LocalDate.now().minusDays(30);
        }
        if (endDate == null) {
            endDate = LocalDate.now();
        }
        
        var stats = taskService.getTaskStatistics(
            startDate.atStartOfDay(), 
            endDate.atTime(23, 59, 59)
        );
        
        Map<String, Object> result = new HashMap<>();
        if (stats != null) {
            result.put("totalTasks", stats.getTotalTasks());
            result.put("successfulTasks", stats.getSuccessfulTasks());
            result.put("failedTasks", stats.getFailedTasks());
            result.put("partialTasks", stats.getPartialTasks());
            result.put("totalCost", stats.getTotalCost());
            result.put("avgExecutionTime", stats.getAvgExecutionTime());
            
            // 计算成功率
            if (stats.getTotalTasks() > 0) {
                double successRate = (double) stats.getSuccessfulTasks() / stats.getTotalTasks() * 100;
                result.put("successRate", Math.round(successRate * 100.0) / 100.0);
            } else {
                result.put("successRate", 0.0);
            }
        }
        
        result.put("startDate", startDate);
        result.put("endDate", endDate);
        
        return ResponseEntity.ok(result);
    }

    /**
     * 获取任务状态分布
     */
    @GetMapping("/status-distribution")
    @Operation(summary = "获取任务状态分布", description = "获取各种状态的任务数量分布")
    public ResponseEntity<Map<String, Long>> getTaskStatusDistribution() {
        Map<String, Long> distribution = new HashMap<>();
        
        // 获取各状态的任务数量
        String[] statuses = {"SUCCESS", "FAILED", "PARTIAL", "RUNNING", "PENDING"};
        for (String status : statuses) {
            List<DailyTask> tasks = taskService.getTasksByStatus(status);
            distribution.put(status, (long) tasks.size());
        }
        
        return ResponseEntity.ok(distribution);
    }

    /**
     * 删除任务
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除任务", description = "删除指定的任务记录")
    public ResponseEntity<Map<String, Object>> deleteTask(
            @Parameter(description = "任务ID") @PathVariable Long id) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            DailyTask task = taskService.getTaskById(id);
            if (task == null) {
                result.put("success", false);
                result.put("message", "任务不存在");
                return ResponseEntity.notFound().build();
            }
            
            // 不允许删除正在运行的任务
            if ("RUNNING".equals(task.getStatus())) {
                result.put("success", false);
                result.put("message", "不能删除正在运行的任务");
                return ResponseEntity.badRequest().body(result);
            }
            
            // TODO: 实现删除逻辑
            result.put("success", true);
            result.put("message", "任务删除成功");
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 获取任务执行趋势
     */
    @GetMapping("/trends")
    @Operation(summary = "获取任务执行趋势", description = "获取最近一段时间的任务执行趋势数据")
    public ResponseEntity<List<Map<String, Object>>> getTaskTrends(
            @Parameter(description = "天数") @RequestParam(defaultValue = "7") int days) {
        
        // TODO: 实现趋势数据查询
        // 这里应该返回每日的任务执行情况
        
        return ResponseEntity.ok(List.of());
    }
}
