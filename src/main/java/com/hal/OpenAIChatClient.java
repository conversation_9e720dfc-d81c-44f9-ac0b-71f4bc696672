package com.hal;


import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.InputStreamReader;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

public class OpenAIChatClient {
    private static final String KEY_FILE_PATH = "C:\\me\\key.txt";  // API密钥文件路径
    private static final String API_URL = "https://api.openai.com/v1/chat/completions";
    private static final ObjectMapper mapper = new ObjectMapper();
    private String apiKey;  // API密钥

    // 构造函数，初始化时从文件读取API密钥
    public OpenAIChatClient() {
        try {
            // 从文件读取API密钥
            this.apiKey = new String(Files.readAllBytes(Paths.get(KEY_FILE_PATH))).trim();
            System.out.println("已从文件加载API密钥");
        } catch (Exception e) {
            System.err.println("无法读取API密钥文件: " + e.getMessage());
            this.apiKey = "";  // 设置为空字符串
        }
    }

    // 代理配置
    private String proxyHost;
    private int proxyPort;
    private boolean useProxy = false;

    // 设置代理
    public void setProxy(String host, int port) {
        this.proxyHost = host;
        this.proxyPort = port;
        this.useProxy = true;
    }

    // 禁用代理
    public void disableProxy() {
        this.useProxy = false;
    }

    // 使用JDK 24的Record类型来表示消息
    public record Message(String role, String content) {
        // Record自动提供getter、equals、hashCode和toString方法
    }

    // 流式对话输出
    public void chat(List<Message> messages) throws Exception {
        // 创建OkHttpClient并配置代理
        OkHttpClient.Builder clientBuilder = new OkHttpClient.Builder();

        // 如果启用了代理，则配置代理
        if (useProxy && proxyHost != null && !proxyHost.isEmpty()) {
            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort));
            clientBuilder.proxy(proxy);
            System.out.println("使用代理: " + proxyHost + ":" + proxyPort);
        }

        OkHttpClient client = clientBuilder.build();

        Map<String, Object> request = new HashMap<>();
        request.put("model", "gpt-3.5-turbo");  // 或 gpt-3.5-turbo
        request.put("stream", true);
        request.put("messages", messages);

        Request httpRequest = new Request.Builder()
                .url(API_URL)
                .header("Authorization", "Bearer " + apiKey)
                .post(RequestBody.create(
                        MediaType.parse("application/json"),
                        mapper.writeValueAsString(request)
                ))
                .build();

        Response response = client.newCall(httpRequest).execute();
        if (!response.isSuccessful()) {
            System.out.println("请求失败：" + response.code());
            return;
        }

        // 实时读取响应，使用模式匹配简化逻辑
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().byteStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.startsWith("data: ")) {
                    String json = line.substring(6).trim();
                    if (json.equals("[DONE]")) break;

                    // 使用Java 8兼容的方式获取响应内容
                    Map<String, Object> data = mapper.readValue(json, Map.class);
                    List<Object> choices = (List<Object>) data.get("choices");
                    if (choices != null && !choices.isEmpty()) {
                        Map<String, Object> choice = (Map<String, Object>) choices.get(0);
                        Map<String, Object> delta = (Map<String, Object>) choice.get("delta");
                        if (delta != null) {
                            String content = (String) delta.get("content");
                            if (content != null) {
                                System.out.print(content);
                            }
                        }
                    }
                }
            }
        }

        System.out.println();  // 换行
    }
}
