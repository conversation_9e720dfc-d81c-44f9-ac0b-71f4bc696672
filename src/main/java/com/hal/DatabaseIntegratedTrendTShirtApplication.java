package com.hal;

import com.hal.database.DatabaseManager;
import com.hal.scheduler.DatabaseIntegratedDailyTrendJob;
import com.hal.scheduler.TaskScheduler;
import com.hal.service.database.*;
import org.quartz.SchedulerException;

import java.time.LocalDateTime;
import java.util.Scanner;

/**
 * 集成数据库的热搜T恤自动化系统主应用
 * 完整版本，包含数据库记录和管理功能
 */
public class DatabaseIntegratedTrendTShirtApplication {
    
    private final DatabaseManager databaseManager;
    private final TaskScheduler taskScheduler;
    private final DailyTaskService taskService;
    private final TrendingTopicService topicService;
    private final DatabaseIntegratedOpenAIService openAIService;
    private final ApiUsageLogService apiLogService;
    private final SystemConfigService configService;
    private final Scanner scanner;
    
    public DatabaseIntegratedTrendTShirtApplication() {
        this.databaseManager = new DatabaseManager();
        this.taskScheduler = new TaskScheduler();
        this.taskService = new DailyTaskService();
        this.topicService = new TrendingTopicService();
        this.openAIService = new DatabaseIntegratedOpenAIService();
        this.apiLogService = new ApiUsageLogService();
        this.configService = new SystemConfigService();
        this.scanner = new Scanner(System.in);
    }
    
    public static void main(String[] args) {
        DatabaseIntegratedTrendTShirtApplication app = new DatabaseIntegratedTrendTShirtApplication();
        app.run();
    }
    
    public void run() {
        try {
            printWelcomeBanner();
            
            // 初始化数据库
            if (!initializeSystem()) {
                System.err.println("❌ 系统初始化失败，程序退出");
                return;
            }
            
            // 主菜单循环
            while (true) {
                showMainMenu();
                int choice = getMenuChoice();
                
                if (!handleMenuChoice(choice)) {
                    break; // 退出程序
                }
            }
            
        } catch (Exception e) {
            System.err.println("❌ 程序运行异常: " + e.getMessage());
            e.printStackTrace();
        } finally {
            shutdown();
        }
    }
    
    /**
     * 打印欢迎横幅
     */
    private void printWelcomeBanner() {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("🚀 热搜T恤自动化系统 - 数据库集成版 v2.0");
        System.out.println("📊 完整的数据记录和分析功能");
        System.out.println("🗄️ PostgreSQL + MyBatis + JDK 24");
        System.out.println("=".repeat(80));
    }
    
    /**
     * 初始化系统
     */
    private boolean initializeSystem() {
        System.out.println("\n🔧 正在初始化系统...");
        
        try {
            // 1. 初始化数据库
            if (!databaseManager.initializeDatabase()) {
                return false;
            }
            
            // 2. 加载系统配置
            configService.refreshCache();
            
            // 3. 显示系统状态
            databaseManager.printStatusReport();
            
            System.out.println("✅ 系统初始化完成");
            return true;
            
        } catch (Exception e) {
            System.err.println("❌ 系统初始化失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 显示主菜单
     */
    private void showMainMenu() {
        System.out.println("\n📋 主菜单:");
        System.out.println("1. 🚀 立即执行任务");
        System.out.println("2. ⏰ 设置定时执行");
        System.out.println("3. 📊 查看任务历史");
        System.out.println("4. 🔍 查看热搜分析");
        System.out.println("5. 🖼️ 查看图片管理");
        System.out.println("6. 🛒 查看商品管理");
        System.out.println("7. 📈 查看API使用统计");
        System.out.println("8. ⚙️ 系统配置管理");
        System.out.println("9. 🗄️ 数据库管理");
        System.out.println("10. 🧹 系统维护");
        System.out.println("0. 👋 退出程序");
        System.out.println("-".repeat(40));
    }
    
    /**
     * 获取菜单选择
     */
    private int getMenuChoice() {
        System.out.print("请选择功能 (0-10): ");
        try {
            return scanner.nextInt();
        } catch (Exception e) {
            scanner.nextLine(); // 清理输入缓冲区
            return -1;
        }
    }
    
    /**
     * 处理菜单选择
     */
    private boolean handleMenuChoice(int choice) {
        try {
            switch (choice) {
                case 0 -> {
                    System.out.println("👋 感谢使用，再见！");
                    return false;
                }
                case 1 -> executeTaskNow();
                case 2 -> setupScheduledTask();
                case 3 -> viewTaskHistory();
                case 4 -> viewTrendingAnalysis();
                case 5 -> viewImageManagement();
                case 6 -> viewProductManagement();
                case 7 -> viewApiUsageStats();
                case 8 -> manageSystemConfig();
                case 9 -> manageDatabaseSystem();
                case 10 -> performSystemMaintenance();
                default -> System.out.println("❌ 无效选择，请重试");
            }
            
            // 等待用户按键继续
            if (choice != 0) {
                System.out.println("\n按回车键继续...");
                scanner.nextLine();
                scanner.nextLine();
            }
            
            return true;
            
        } catch (Exception e) {
            System.err.println("❌ 操作失败: " + e.getMessage());
            return true;
        }
    }
    
    /**
     * 立即执行任务
     */
    private void executeTaskNow() {
        System.out.println("\n🚀 立即执行热搜T恤生成任务");
        System.out.println("⚠️ 这将消耗API配额和费用");
        System.out.print("确认执行? (y/N): ");
        
        String confirm = scanner.nextLine().trim().toLowerCase();
        if (!"y".equals(confirm) && !"yes".equals(confirm)) {
            System.out.println("❌ 任务已取消");
            return;
        }
        
        try {
            DatabaseIntegratedDailyTrendJob job = new DatabaseIntegratedDailyTrendJob();
            job.execute(null);
        } catch (Exception e) {
            System.err.println("❌ 任务执行失败: " + e.getMessage());
        }
    }
    
    /**
     * 设置定时执行
     */
    private void setupScheduledTask() throws SchedulerException {
        System.out.println("\n⏰ 设置定时任务");
        
        if (taskScheduler.isRunning()) {
            System.out.println("📋 当前调度器状态: 运行中");
            System.out.print("是否停止当前调度器? (y/N): ");
            String confirm = scanner.nextLine().trim().toLowerCase();
            if ("y".equals(confirm) || "yes".equals(confirm)) {
                taskScheduler.stopScheduler();
            }
        }
        
        System.out.print("请输入执行时间 (HH:MM, 如 09:30): ");
        String time = scanner.nextLine().trim();
        
        if (!time.matches("\\d{2}:\\d{2}")) {
            System.out.println("❌ 时间格式无效");
            return;
        }
        
        try {
            taskScheduler.scheduleEnhancedDailyJob(time);
            
            // 保存到配置
            configService.setConfig("scheduler.daily_job_time", time);
            
            System.out.println("✅ 定时任务设置成功");
        } catch (Exception e) {
            System.err.println("❌ 设置定时任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 查看任务历史
     */
    private void viewTaskHistory() {
        System.out.println("\n📊 任务执行历史");
        System.out.println("=");
        
        var recentTasks = taskService.getRecentTasks(10);
        if (recentTasks.isEmpty()) {
            System.out.println("📭 没有任务执行记录");
            return;
        }
        
        for (var task : recentTasks) {
            taskService.printTaskReport(task);
            System.out.println();
        }
        
        // 显示统计信息
        var stats = taskService.getTaskStatistics(
            LocalDateTime.now().minusDays(30),
            LocalDateTime.now()
        );
        
        if (stats != null) {
            System.out.println("📈 最近30天统计:");
            System.out.println("总任务数: " + stats.getTotalTasks());
            System.out.println("成功任务: " + stats.getSuccessfulTasks());
            System.out.println("失败任务: " + stats.getFailedTasks());
            System.out.println("总成本: $" + String.format("%.2f", stats.getTotalCost()));
            System.out.println("平均执行时间: " + String.format("%.1f", stats.getAvgExecutionTime() / 1000) + "秒");
        }
    }
    
    /**
     * 查看热搜分析
     */
    private void viewTrendingAnalysis() {
        System.out.println("\n🔍 热搜话题分析");
        System.out.println("=");
        
        // 显示热门话题
        var popularTopics = topicService.getPopularTopics(7, 2, 10);
        if (!popularTopics.isEmpty()) {
            System.out.println("🔥 最近7天热门话题:");
            for (var topic : popularTopics) {
                System.out.println("  " + topic.getTopic() + " (出现" + topic.getFrequency() + "次)");
            }
        }
        
        // 显示类型统计
        var typeStats = topicService.getTopicTypeStatistics(
            LocalDateTime.now().minusDays(30),
            LocalDateTime.now()
        );
        
        if (!typeStats.isEmpty()) {
            System.out.println("\n📊 话题类型分布 (最近30天):");
            for (var stat : typeStats) {
                System.out.println("  " + getTypeEmoji(stat.getTopicType()) + " " + 
                    stat.getTopicType() + ": " + stat.getCount() + " 个 (完成率: " + 
                    String.format("%.1f", stat.getCompletionRate()) + "%)");
            }
        }
    }
    
    /**
     * 查看图片管理
     */
    private void viewImageManagement() {
        System.out.println("\n🖼️ 图片管理");
        System.out.println("=");
        
        // 显示最近生成的图片
        var recentImages = openAIService.getImageHistory(10);
        if (!recentImages.isEmpty()) {
            System.out.println("📸 最近生成的图片:");
            for (var image : recentImages) {
                System.out.println("  ID: " + image.getId() + 
                    " | 文件: " + image.getFileName() + 
                    " | 状态: " + image.getStatus() + 
                    " | 成本: $" + String.format("%.4f", image.getGenerationCost()));
            }
        }
        
        // 显示存储统计
        var storageStats = openAIService.getStorageUsage(30);
        if (storageStats != null) {
            System.out.println("\n💾 存储使用统计 (最近30天):");
            System.out.println("总图片数: " + storageStats.getTotalImages());
            System.out.println("总大小: " + storageStats.getFormattedTotalSize());
            System.out.println("本地存储: " + storageStats.getLocalStored());
            System.out.println("云端存储: " + storageStats.getCloudStored());
            System.out.println("平均大小: " + String.format("%.1f KB", storageStats.getAvgFileSize() / 1024));
        }
    }
    
    /**
     * 查看商品管理
     */
    private void viewProductManagement() {
        System.out.println("\n🛒 商品管理");
        System.out.println("=");
        System.out.println("⚠️ 商品管理功能开发中...");
        // TODO: 实现商品管理功能
    }
    
    /**
     * 查看API使用统计
     */
    private void viewApiUsageStats() {
        System.out.println("\n📈 API使用统计");
        apiLogService.printUsageReport(7);
    }
    
    /**
     * 管理系统配置
     */
    private void manageSystemConfig() {
        System.out.println("\n⚙️ 系统配置管理");
        System.out.println("1. 查看所有配置");
        System.out.println("2. 修改配置");
        System.out.println("3. 刷新配置缓存");
        System.out.print("请选择操作 (1-3): ");
        
        int choice = scanner.nextInt();
        scanner.nextLine();
        
        switch (choice) {
            case 1 -> configService.printAllConfigs();
            case 2 -> modifyConfig();
            case 3 -> {
                configService.refreshCache();
                System.out.println("✅ 配置缓存已刷新");
            }
            default -> System.out.println("❌ 无效选择");
        }
    }
    
    /**
     * 修改配置
     */
    private void modifyConfig() {
        System.out.print("请输入配置键: ");
        String key = scanner.nextLine().trim();
        
        System.out.print("请输入新值: ");
        String value = scanner.nextLine().trim();
        
        try {
            configService.setConfig(key, value);
            System.out.println("✅ 配置已更新");
        } catch (Exception e) {
            System.err.println("❌ 配置更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 管理数据库系统
     */
    private void manageDatabaseSystem() {
        System.out.println("\n🗄️ 数据库管理");
        System.out.println("1. 查看数据库状态");
        System.out.println("2. 测试数据库连接");
        System.out.println("3. 运行数据库迁移");
        System.out.println("4. 执行数据库维护");
        System.out.print("请选择操作 (1-4): ");
        
        int choice = scanner.nextInt();
        scanner.nextLine();
        
        switch (choice) {
            case 1 -> databaseManager.printStatusReport();
            case 2 -> {
                boolean connected = databaseManager.testConnection();
                System.out.println(connected ? "✅ 数据库连接正常" : "❌ 数据库连接失败");
            }
            case 3 -> {
                boolean success = databaseManager.runMigrations();
                System.out.println(success ? "✅ 迁移完成" : "❌ 迁移失败");
            }
            case 4 -> databaseManager.performMaintenance();
            default -> System.out.println("❌ 无效选择");
        }
    }
    
    /**
     * 执行系统维护
     */
    private void performSystemMaintenance() {
        System.out.println("\n🧹 系统维护");
        System.out.println("⚠️ 这将清理旧数据，确认继续? (y/N): ");
        
        String confirm = scanner.nextLine().trim().toLowerCase();
        if (!"y".equals(confirm) && !"yes".equals(confirm)) {
            System.out.println("❌ 维护已取消");
            return;
        }
        
        try {
            // 执行数据库维护
            databaseManager.performMaintenance();
            
            // 清理失败的图片
            int cleanedImages = openAIService.cleanupFailedImages(7);
            System.out.println("🖼️ 清理了 " + cleanedImages + " 个失败的图片记录");
            
            System.out.println("✅ 系统维护完成");
            
        } catch (Exception e) {
            System.err.println("❌ 系统维护失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取类型表情符号
     */
    private String getTypeEmoji(String type) {
        return switch (type) {
            case "technology" -> "💻";
            case "environment" -> "🌍";
            case "politics" -> "🏛️";
            case "finance" -> "💰";
            case "entertainment" -> "🎬";
            case "sports" -> "⚽";
            case "general" -> "📰";
            default -> "❓";
        };
    }
    
    /**
     * 关闭系统
     */
    private void shutdown() {
        try {
            System.out.println("\n🔄 正在关闭系统...");
            
            // 停止调度器
            if (taskScheduler.isRunning()) {
                taskScheduler.stopScheduler();
            }
            
            // 关闭数据库连接
            databaseManager.shutdown();
            
            // 关闭扫描器
            scanner.close();
            
            System.out.println("✅ 系统已安全关闭");
            
        } catch (Exception e) {
            System.err.println("⚠️ 关闭系统时出现警告: " + e.getMessage());
        }
    }
}
