package com.hal;

import com.hal.config.AmazonConfig;
import com.hal.config.TwitterConfig;
import com.hal.scheduler.TaskScheduler;
import com.hal.service.AmazonListingService;
import com.hal.service.OpenAIImageService;
import com.hal.service.TwitterTrendService;
import org.quartz.SchedulerException;

import java.util.List;
import java.util.Scanner;

/**
 * 热搜T恤自动化系统完整版主应用
 *
 * 集成了真实的Twitter API和Amazon Selling Partner API
 * 实现完整的自动化流程：
 * 1. 获取Twitter热搜 → 2. 生成AI图片 → 3. 上架到Amazon
 */
public class TrendTShirtMainApp {
    private static TaskScheduler taskScheduler;
    private static TwitterConfig twitterConfig;
    private static AmazonConfig amazonConfig;

    public static void main(String[] args) {
        System.out.println("🎯 热搜T恤自动化系统 - 完整版");
        System.out.println("=".repeat(60));

        try {
            // 初始化配置
            initializeConfigs();

            // 检查配置状态
            checkConfigStatus();

            // 初始化任务调度器
            taskScheduler = new TaskScheduler();

            // 显示菜单
            showMainMenu();

            // 处理用户输入
            handleUserInput();

        } catch (Exception e) {
            System.err.println("❌ 系统启动失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void initializeConfigs() {
        System.out.println("🔧 正在初始化配置...");
        twitterConfig = new TwitterConfig();
        amazonConfig = new AmazonConfig();
        System.out.println("✅ 配置初始化完成");
    }

    private static void checkConfigStatus() {
        System.out.println("\n📊 系统配置状态检查:");
        System.out.println("-".repeat(50));

        // 检查Twitter配置
        System.out.println("📱 Twitter API配置:");
        twitterConfig.printStatus();

        System.out.println();

        // 检查Amazon配置
        System.out.println("🛒 Amazon API配置:");
        amazonConfig.printStatus();

        System.out.println();

        // 检查OpenAI配置
        System.out.println("🎨 OpenAI API配置:");
        try {
            OpenAIImageService imageService = new OpenAIImageService();
            System.out.println("- API密钥: 已配置");
        } catch (Exception e) {
            System.out.println("- API密钥: ❌ 未配置或无效");
        }

        System.out.println("-".repeat(50));
    }

    private static void showMainMenu() {
        System.out.println("\n📋 可用操作:");
        System.out.println("1. 🚀 启动定时任务 (每天上午9点执行)");
        System.out.println("2. ⚡ 立即执行一次完整流程");
        System.out.println("3. 🧪 测试各个组件");
        System.out.println("4. ⏰ 设置自定义执行时间");
        System.out.println("5. 📊 查看调度器状态");
        System.out.println("6. ⏸️ 暂停/恢复任务");
        System.out.println("7. 🔧 重新检查配置");
        System.out.println("8. 🛑 停止系统");
        System.out.println("9. 📖 显示帮助");
        System.out.println("0. 👋 退出");
        System.out.println("-".repeat(50));
    }

    private static void handleUserInput() {
        Scanner scanner = new Scanner(System.in);

        while (true) {
            System.out.print("\n请选择操作 (输入数字): ");
            String input = scanner.nextLine().trim();

            try {
                // 使用JDK 24的Switch表达式
                switch (input) {
                    case "1" -> startScheduler();
                    case "2" -> executeNow();
                    case "3" -> testComponents();
                    case "4" -> setCustomSchedule(scanner);
                    case "5" -> showStatus();
                    case "6" -> toggleTask();
                    case "7" -> recheckConfigs();
                    case "8" -> stopScheduler();
                    case "9" -> showHelp();
                    case "0" -> {
                        exit();
                        return;
                    }
                    default -> System.out.println("❌ 无效选项，请重新输入");
                }
            } catch (Exception e) {
                System.err.println("❌ 操作失败: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    private static void startScheduler() throws SchedulerException {
        System.out.println("\n🚀 启动定时任务调度器...");
        taskScheduler.start();
        System.out.println("✅ 定时任务已启动，将在每天上午9:00执行完整流程");
        System.out.println("💡 流程包括：获取Twitter热搜 → 生成AI图片 → 上架到Amazon");
    }

    private static void executeNow() throws SchedulerException {
        System.out.println("\n⚡ 立即执行完整流程...");
        System.out.println("📝 这将执行以下步骤:");
        System.out.println("   1. 📱 获取Twitter热搜前10条");
        System.out.println("   2. 🎨 为每个热搜生成AI T恤设计");
        System.out.println("   3. 🛒 将生成的商品上架到Amazon");
        System.out.println();

        System.out.print("确认执行吗？(y/N): ");
        Scanner scanner = new Scanner(System.in);
        String confirm = scanner.nextLine().trim().toLowerCase();

        if ("y".equals(confirm) || "yes".equals(confirm)) {
            taskScheduler.executeNow();
            System.out.println("✅ 任务已触发，请查看执行日志");
        } else {
            System.out.println("❌ 操作已取消");
        }
    }

    private static void testComponents() {
        System.out.println("\n🧪 开始测试各个组件...");
        System.out.println("=".repeat(50));

        // 测试Twitter服务
        testTwitterService();

        // 测试OpenAI服务
        testOpenAIService();

        // 测试Amazon服务
        testAmazonService();

        System.out.println("=".repeat(50));
        System.out.println("✅ 组件测试完成");
    }

    private static void testTwitterService() {
        System.out.println("\n📱 测试Twitter热搜服务...");
        try {
            TwitterTrendService service = new TwitterTrendService();
            service.setProxy("127.0.0.1", 7890);

            List<String> trends = service.getTrendingTopics();
            if (!trends.isEmpty()) {
                System.out.println("✅ Twitter服务测试成功，获取到 " + trends.size() + " 个热搜");
            } else {
                System.out.println("⚠️ Twitter服务返回空结果");
            }

        } catch (Exception e) {
            System.err.println("❌ Twitter服务测试失败: " + e.getMessage());
        }
    }

    private static void testOpenAIService() {
        System.out.println("\n🎨 测试OpenAI图片生成服务...");
        try {
            OpenAIImageService service = new OpenAIImageService();
            service.setProxy("127.0.0.1", 7890);

            System.out.println("正在生成测试图片...");
            String imageUrl = service.generateTShirtImage("人工智能");

            if (imageUrl != null) {
                System.out.println("✅ OpenAI服务测试成功");
                System.out.println("生成的图片URL: " + imageUrl);
            } else {
                System.out.println("❌ OpenAI服务测试失败");
            }

        } catch (Exception e) {
            System.err.println("❌ OpenAI服务测试失败: " + e.getMessage());
        }
    }

    private static void testAmazonService() {
        System.out.println("\n🛒 测试Amazon上架服务...");
        try {
            AmazonListingService service = new AmazonListingService();
            service.setProxy("127.0.0.1", 7890);

            AmazonListingService.ProductInfo testProduct =
                new AmazonListingService.ProductInfo(
                    "测试话题",
                    "https://example.com/test-image.jpg",
                    "test_image.jpg"
                );

            String listingId = service.listProduct(testProduct);

            if (listingId != null) {
                System.out.println("✅ Amazon服务测试成功");
                System.out.println("Listing ID: " + listingId);
            } else {
                System.out.println("❌ Amazon服务测试失败");
            }

        } catch (Exception e) {
            System.err.println("❌ Amazon服务测试失败: " + e.getMessage());
        }
    }

    private static void setCustomSchedule(Scanner scanner) throws SchedulerException {
        System.out.println("\n⏰ 设置自定义执行时间");
        System.out.println("Cron表达式格式: 秒 分 时 日 月 周");
        System.out.println("示例:");
        System.out.println("  每天12:00: 0 0 12 * * ?");
        System.out.println("  每小时: 0 0 * * * ?");
        System.out.println("  每30分钟: 0 */30 * * * ?");

        System.out.print("请输入Cron表达式: ");
        String cronExpression = scanner.nextLine().trim();

        System.out.print("请输入任务描述: ");
        String description = scanner.nextLine().trim();

        taskScheduler.scheduleCustomTime(cronExpression, description);
        System.out.println("✅ 自定义任务已设置");
    }

    private static void showStatus() throws SchedulerException {
        System.out.println("\n📊 系统状态:");
        taskScheduler.printStatus();
    }

    private static void toggleTask() throws SchedulerException {
        System.out.println("\n⏸️ 任务控制:");
        System.out.println("1. 暂停任务");
        System.out.println("2. 恢复任务");

        Scanner scanner = new Scanner(System.in);
        System.out.print("请选择操作 (1/2): ");
        String choice = scanner.nextLine().trim();

        // 使用JDK 24的Switch表达式
        switch (choice) {
            case "1" -> taskScheduler.pauseJob("dailyTrendJob", "trendGroup");
            case "2" -> taskScheduler.resumeJob("dailyTrendJob", "trendGroup");
            default -> System.out.println("❌ 无效选择");
        }
    }

    private static void recheckConfigs() {
        System.out.println("\n🔄 重新检查配置...");
        initializeConfigs();
        checkConfigStatus();
    }

    private static void stopScheduler() throws SchedulerException {
        System.out.println("\n🛑 停止调度器...");
        taskScheduler.shutdown();
        System.out.println("✅ 调度器已停止");
    }

    private static void showHelp() {
        System.out.println("\n📖 系统使用指南:");
        System.out.println("=".repeat(60));
        System.out.println("🎯 系统功能:");
        System.out.println("  这个系统会自动执行以下流程:");
        System.out.println("  1. 📱 获取Twitter热搜前10条信息");
        System.out.println("  2. 🎨 使用OpenAI DALL-E生成讽刺搞笑风格的T恤图片");
        System.out.println("  3. 🛒 将生成的T恤商品自动上架到Amazon");
        System.out.println();
        System.out.println("📋 使用前准备:");
        System.out.println("  - 配置OpenAI API密钥 (C:\\me\\key.txt)");
        System.out.println("  - 配置Twitter API密钥 (C:\\me\\twitter_keys.txt)");
        System.out.println("  - 配置Amazon API密钥 (C:\\me\\amazon_keys.txt)");
        System.out.println("  - 设置代理（如果需要）");
        System.out.println();
        System.out.println("⚠️ 重要提醒:");
        System.out.println("  - 确保遵守各平台的使用条款");
        System.out.println("  - 商品内容应符合法律法规要求");
        System.out.println("  - 建议先测试单次执行，确认无误后再启用定时任务");
        System.out.println("  - 注意API调用成本，特别是OpenAI图片生成");
        System.out.println("=".repeat(60));
    }

    private static void exit() {
        try {
            if (taskScheduler != null) {
                taskScheduler.shutdown();
            }
            System.out.println("\n👋 感谢使用热搜T恤自动化系统！");
            System.out.println("🎉 系统已安全关闭");
            System.exit(0);
        } catch (SchedulerException e) {
            System.err.println("❌ 关闭系统时出错: " + e.getMessage());
            System.exit(1);
        }
    }
}
