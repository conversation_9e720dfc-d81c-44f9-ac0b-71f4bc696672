package com.hal;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 热搜T恤自动化系统 - Web应用主类
 */
@SpringBootApplication
@EnableScheduling
@EnableAsync
@EnableTransactionManagement
public class TrendTShirtWebApplication {

    public static void main(String[] args) {
        System.out.println("🚀 启动热搜T恤自动化系统 - Web版本...");
        System.out.println("📊 集成数据库 + RESTful API + Swagger文档");
        System.out.println("🌐 访问地址: http://localhost:8080/api");
        System.out.println("📖 API文档: http://localhost:8080/api/swagger-ui.html");
        System.out.println("=".repeat(60));
        
        SpringApplication.run(TrendTShirtWebApplication.class, args);
        
        System.out.println("✅ 系统启动完成！");
    }
}
