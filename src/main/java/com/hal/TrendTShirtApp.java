package com.hal;

import com.hal.scheduler.TaskScheduler;
import org.quartz.SchedulerException;

import java.util.Scanner;

/**
 * 热搜T恤自动化系统主应用
 * 
 * 功能：
 * 1. 每天定时获取推特热搜前10条
 * 2. 使用OpenAI生成讽刺搞笑风格的T恤图片
 * 3. 自动上架到亚马逊
 */
public class TrendTShirtApp {
    private static TaskScheduler taskScheduler;

    public static void main(String[] args) {
        System.out.println("🎯 热搜T恤自动化系统");
        System.out.println("=".repeat(50));
        
        try {
            // 初始化任务调度器
            taskScheduler = new TaskScheduler();
            
            // 显示菜单
            showMenu();
            
            // 处理用户输入
            handleUserInput();
            
        } catch (Exception e) {
            System.err.println("❌ 系统启动失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void showMenu() {
        System.out.println("\n📋 可用操作:");
        System.out.println("1. 启动定时任务 (每天上午9点执行)");
        System.out.println("2. 立即执行一次任务");
        System.out.println("3. 设置自定义执行时间");
        System.out.println("4. 查看调度器状态");
        System.out.println("5. 暂停任务");
        System.out.println("6. 恢复任务");
        System.out.println("7. 停止系统");
        System.out.println("8. 显示帮助");
        System.out.println("0. 退出");
        System.out.println("-".repeat(50));
    }

    private static void handleUserInput() {
        Scanner scanner = new Scanner(System.in);
        
        while (true) {
            System.out.print("\n请选择操作 (输入数字): ");
            String input = scanner.nextLine().trim();
            
            try {
                switch (input) {
                    case "1":
                        startScheduler();
                        break;
                    case "2":
                        executeNow();
                        break;
                    case "3":
                        setCustomSchedule(scanner);
                        break;
                    case "4":
                        showStatus();
                        break;
                    case "5":
                        pauseJob();
                        break;
                    case "6":
                        resumeJob();
                        break;
                    case "7":
                        stopScheduler();
                        break;
                    case "8":
                        showHelp();
                        break;
                    case "0":
                        exit();
                        return;
                    default:
                        System.out.println("❌ 无效选项，请重新输入");
                        break;
                }
            } catch (Exception e) {
                System.err.println("❌ 操作失败: " + e.getMessage());
            }
        }
    }

    private static void startScheduler() throws SchedulerException {
        System.out.println("\n🚀 启动定时任务调度器...");
        taskScheduler.start();
        System.out.println("✅ 定时任务已启动，将在每天上午9:00执行");
    }

    private static void executeNow() throws SchedulerException {
        System.out.println("\n⚡ 立即执行任务...");
        taskScheduler.executeNow();
        System.out.println("✅ 任务已触发，请查看执行日志");
    }

    private static void setCustomSchedule(Scanner scanner) throws SchedulerException {
        System.out.println("\n⏰ 设置自定义执行时间");
        System.out.println("Cron表达式格式: 秒 分 时 日 月 周");
        System.out.println("示例:");
        System.out.println("  每天12:00: 0 0 12 * * ?");
        System.out.println("  每小时: 0 0 * * * ?");
        System.out.println("  每30分钟: 0 */30 * * * ?");
        
        System.out.print("请输入Cron表达式: ");
        String cronExpression = scanner.nextLine().trim();
        
        System.out.print("请输入任务描述: ");
        String description = scanner.nextLine().trim();
        
        taskScheduler.scheduleCustomTime(cronExpression, description);
        System.out.println("✅ 自定义任务已设置");
    }

    private static void showStatus() throws SchedulerException {
        taskScheduler.printStatus();
    }

    private static void pauseJob() throws SchedulerException {
        System.out.println("\n⏸️ 暂停任务...");
        taskScheduler.pauseJob("dailyTrendJob", "trendGroup");
    }

    private static void resumeJob() throws SchedulerException {
        System.out.println("\n▶️ 恢复任务...");
        taskScheduler.resumeJob("dailyTrendJob", "trendGroup");
    }

    private static void stopScheduler() throws SchedulerException {
        System.out.println("\n🛑 停止调度器...");
        taskScheduler.shutdown();
        System.out.println("✅ 调度器已停止");
    }

    private static void showHelp() {
        System.out.println("\n📖 系统说明:");
        System.out.println("这个系统会自动执行以下流程:");
        System.out.println("1. 📱 获取推特热搜前10条信息");
        System.out.println("2. 🎨 使用OpenAI DALL-E生成讽刺搞笑风格的T恤图片");
        System.out.println("3. 🛒 将生成的T恤商品自动上架到亚马逊");
        System.out.println();
        System.out.println("📋 使用前准备:");
        System.out.println("- 确保C:\\me\\key.txt文件包含有效的OpenAI API密钥");
        System.out.println("- 配置代理设置（如果需要）");
        System.out.println("- 设置亚马逊卖家账户信息");
        System.out.println();
        System.out.println("⚠️ 注意事项:");
        System.out.println("- 请确保遵守相关平台的使用条款");
        System.out.println("- 商品内容应符合法律法规要求");
        System.out.println("- 建议先测试单次执行，确认无误后再启用定时任务");
    }

    private static void exit() {
        try {
            if (taskScheduler != null) {
                taskScheduler.shutdown();
            }
            System.out.println("\n👋 感谢使用热搜T恤自动化系统！");
            System.exit(0);
        } catch (SchedulerException e) {
            System.err.println("❌ 关闭系统时出错: " + e.getMessage());
            System.exit(1);
        }
    }
}
