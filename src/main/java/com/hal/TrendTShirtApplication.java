package com.hal;

import com.hal.config.SystemConfig;
import com.hal.scheduler.EnhancedDailyTrendJob;
import com.hal.scheduler.TaskScheduler;
import com.hal.service.*;
import org.quartz.SchedulerException;

import java.util.Scanner;

/**
 * 热搜T恤自动化系统 - 完整版主应用
 * 
 * 功能特性：
 * 1. 🔥 智能热搜获取 - Twitter API集成
 * 2. 🎨 AI图片生成 - OpenAI DALL-E 3集成，专业T恤设计提示词
 * 3. 🛒 自动商品上架 - Amazon Selling Partner API集成
 * 4. 💰 成本控制 - 智能配额管理和预算控制
 * 5. 🔄 重试机制 - 智能错误处理和重试策略
 * 6. ⏰ 定时任务 - 每日自动执行
 * 7. 📊 实时监控 - 使用统计和成本追踪
 * 8. ⚙️ 灵活配置 - 可配置的系统参数
 */
public class TrendTShirtApplication {
    
    private static SystemConfig systemConfig;
    private static TaskScheduler taskScheduler;
    private static Scanner scanner;
    
    public static void main(String[] args) {
        System.out.println("🚀 热搜T恤自动化系统 - 完整版");
        System.out.println("=".repeat(60));
        System.out.println("📅 版本: 2.0 Enhanced");
        System.out.println("🔧 Java版本: " + System.getProperty("java.version"));
        System.out.println("=".repeat(60));
        
        try {
            // 初始化系统
            initializeSystem();
            
            // 显示系统状态
            displaySystemStatus();
            
            // 启动主菜单
            runMainMenu();
            
        } catch (Exception e) {
            System.err.println("❌ 系统启动失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            cleanup();
        }
    }
    
    /**
     * 初始化系统
     */
    private static void initializeSystem() {
        System.out.println("\n🔧 正在初始化系统...");
        
        // 1. 加载配置
        systemConfig = new SystemConfig();
        System.out.println("✅ 系统配置加载完成");
        
        // 2. 初始化任务调度器
        try {
            taskScheduler = new TaskScheduler();
            System.out.println("✅ 任务调度器初始化完成");
        } catch (Exception e) {
            System.err.println("⚠️ 任务调度器初始化失败: " + e.getMessage());
        }
        
        // 3. 初始化扫描器
        scanner = new Scanner(System.in);
        
        // 4. 创建必要的目录
        createDirectories();
        
        System.out.println("✅ 系统初始化完成");
    }
    
    /**
     * 创建必要的目录
     */
    private static void createDirectories() {
        try {
            java.nio.file.Files.createDirectories(
                java.nio.file.Paths.get(systemConfig.getFileConfig().getImageDirectory())
            );
            System.out.println("✅ 图片目录创建完成");
        } catch (Exception e) {
            System.err.println("⚠️ 目录创建失败: " + e.getMessage());
        }
    }
    
    /**
     * 显示系统状态
     */
    private static void displaySystemStatus() {
        System.out.println("\n📊 系统状态检查:");
        System.out.println("-".repeat(50));
        
        // 检查API配置状态
        checkAPIStatus();
        
        // 显示成本控制状态
        displayCostStatus();
        
        // 显示系统配置
        if (systemConfig != null) {
            systemConfig.printAllConfig();
        }
    }
    
    /**
     * 检查API状态
     */
    private static void checkAPIStatus() {
        System.out.println("🔍 API配置状态:");
        
        // Twitter API
        try {
            TwitterTrendService twitterService = new TwitterTrendService();
            System.out.println("📱 Twitter API: " + (twitterService != null ? "✅ 已配置" : "❌ 未配置"));
        } catch (Exception e) {
            System.out.println("📱 Twitter API: ❌ 配置错误 - " + e.getMessage());
        }
        
        // OpenAI API
        try {
            OpenAIImageService openAIService = new OpenAIImageService();
            System.out.println("🎨 OpenAI API: ✅ 已配置");
        } catch (Exception e) {
            System.out.println("🎨 OpenAI API: ❌ 配置错误 - " + e.getMessage());
        }
        
        // Amazon API
        try {
            AmazonListingService amazonService = new AmazonListingService();
            System.out.println("🛒 Amazon API: ✅ 已配置");
        } catch (Exception e) {
            System.out.println("🛒 Amazon API: ❌ 配置错误 - " + e.getMessage());
        }
    }
    
    /**
     * 显示成本控制状态
     */
    private static void displayCostStatus() {
        try {
            CostControlService costControl = new CostControlService();
            costControl.printUsageStats();
        } catch (Exception e) {
            System.err.println("⚠️ 无法获取成本状态: " + e.getMessage());
        }
    }
    
    /**
     * 运行主菜单
     */
    private static void runMainMenu() {
        while (true) {
            showMainMenu();
            
            try {
                System.out.print("请选择操作 (0-9): ");
                int choice = scanner.nextInt();
                scanner.nextLine(); // 消费换行符
                
                switch (choice) {
                    case 0:
                        System.out.println("👋 感谢使用热搜T恤自动化系统，再见！");
                        return;
                    case 1:
                        executeManualTask();
                        break;
                    case 2:
                        scheduleAutomaticTask();
                        break;
                    case 3:
                        viewSystemStatus();
                        break;
                    case 5:
                        manageConfiguration();
                        break;
                    case 6:
                        viewUsageStatistics();
                        break;
                    case 7:
                        manageScheduledTasks();
                        break;
                    case 8:
                        runDiagnostics();
                        break;
                    case 9:
                        showHelp();
                        break;
                    default:
                        System.out.println("❌ 无效选择，请重试");
                        break;
                }
                
                if (choice != 0) {
                    System.out.println("\n按回车键继续...");
                    scanner.nextLine();
                }
                
            } catch (Exception e) {
                System.err.println("❌ 操作执行失败: " + e.getMessage());
                scanner.nextLine(); // 清理输入缓冲区
            }
        }
    }
    
    /**
     * 显示主菜单
     */
    private static void showMainMenu() {
        System.out.println("\n" + "=".repeat(60));
        System.out.println("🎯 热搜T恤自动化系统 - 主菜单");
        System.out.println("=".repeat(60));
        System.out.println("1. 🚀 立即执行热搜T恤生成任务");
        System.out.println("2. ⏰ 设置定时自动执行");
        System.out.println("3. 📊 查看系统状态");
        System.out.println("4. 🧪 测试系统组件");
        System.out.println("5. ⚙️ 管理系统配置");
        System.out.println("6. 📈 查看使用统计");
        System.out.println("7. 📅 管理定时任务");
        System.out.println("8. 🔧 运行系统诊断");
        System.out.println("9. ❓ 帮助信息");
        System.out.println("0. 🚪 退出系统");
        System.out.println("-".repeat(60));
    }
    
    /**
     * 执行手动任务
     */
    private static void executeManualTask() {
        System.out.println("\n🚀 开始执行热搜T恤生成任务...");
        
        try {
            EnhancedDailyTrendJob job = new EnhancedDailyTrendJob();
            job.execute(null);
            System.out.println("✅ 任务执行完成！");
            
        } catch (Exception e) {
            System.err.println("❌ 任务执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 设置定时自动执行
     */
    private static void scheduleAutomaticTask() {
        System.out.println("\n⏰ 设置定时任务...");
        
        if (taskScheduler == null) {
            System.err.println("❌ 任务调度器未初始化");
            return;
        }
        
        try {
            System.out.print("请输入执行时间 (格式: HH:mm, 如 09:30): ");
            String timeInput = scanner.nextLine().trim();
            
            if (!timeInput.matches("\\d{2}:\\d{2}")) {
                System.err.println("❌ 时间格式错误，请使用 HH:mm 格式");
                return;
            }
            
            // 停止现有任务
            taskScheduler.stopScheduler();
            
            // 更新配置
            systemConfig.set("scheduler.daily_job_time", timeInput);
            systemConfig.saveConfig();
            
            // 重新启动调度器
            taskScheduler = new TaskScheduler();
            taskScheduler.scheduleEnhancedDailyJob(timeInput);
            
            System.out.println("✅ 定时任务设置成功！");
            System.out.println("📅 每日执行时间: " + timeInput);
            System.out.println("🌍 时区: " + systemConfig.getSchedulerConfig().getTimezone());
            
        } catch (Exception e) {
            System.err.println("❌ 定时任务设置失败: " + e.getMessage());
        }
    }
    
    /**
     * 查看系统状态
     */
    private static void viewSystemStatus() {
        System.out.println("\n📊 系统状态详情:");
        displaySystemStatus();
        
        // 显示调度器状态
        if (taskScheduler != null) {
            try {
                System.out.println("\n⏰ 调度器状态:");
                System.out.println("运行状态: " + (taskScheduler.isRunning() ? "✅ 运行中" : "❌ 已停止"));
                // 可以添加更多调度器状态信息
            } catch (Exception e) {
                System.err.println("⚠️ 无法获取调度器状态: " + e.getMessage());
            }
        }
    }

    
    /**
     * 管理系统配置
     */
    private static void manageConfiguration() {
        System.out.println("\n⚙️ 系统配置管理:");
        System.out.println("1. 查看当前配置");
        System.out.println("2. 修改代理设置");
        System.out.println("3. 修改成本控制");
        System.out.println("4. 修改处理限制");
        System.out.println("5. 重置为默认配置");
        System.out.println("0. 返回主菜单");
        
        System.out.print("请选择操作: ");
        int choice = scanner.nextInt();
        scanner.nextLine();
        
        switch (choice) {
            case 1 -> systemConfig.printAllConfig();
            case 2 -> modifyProxySettings();
            case 3 -> modifyCostControl();
            case 4 -> modifyProcessingLimits();
            case 5 -> resetToDefaultConfig();
            case 0 -> { /* 返回主菜单 */ }
            default -> System.out.println("❌ 无效选择");
        }
    }
    
    /**
     * 修改代理设置
     */
    private static void modifyProxySettings() {
        System.out.println("\n🌐 代理设置:");
        
        System.out.print("启用代理? (y/N): ");
        boolean enabled = "y".equalsIgnoreCase(scanner.nextLine().trim());
        systemConfig.set("proxy.enabled", String.valueOf(enabled));
        
        if (enabled) {
            System.out.print("代理主机 (当前: " + systemConfig.getString("proxy.host") + "): ");
            String host = scanner.nextLine().trim();
            if (!host.isEmpty()) {
                systemConfig.set("proxy.host", host);
            }
            
            System.out.print("代理端口 (当前: " + systemConfig.getString("proxy.port") + "): ");
            String port = scanner.nextLine().trim();
            if (!port.isEmpty()) {
                systemConfig.set("proxy.port", port);
            }
        }
        
        systemConfig.saveConfig();
        System.out.println("✅ 代理设置已保存");
    }
    
    /**
     * 修改成本控制
     */
    private static void modifyCostControl() {
        System.out.println("\n💰 成本控制设置:");
        
        System.out.print("每日最大OpenAI请求数 (当前: " + systemConfig.getInt("cost.max_daily_openai_requests") + "): ");
        String requests = scanner.nextLine().trim();
        if (!requests.isEmpty()) {
            systemConfig.set("cost.max_daily_openai_requests", requests);
        }
        
        System.out.print("每日最大预算 (当前: $" + systemConfig.getDouble("cost.max_daily_budget") + "): ");
        String budget = scanner.nextLine().trim();
        if (!budget.isEmpty()) {
            systemConfig.set("cost.max_daily_budget", budget);
        }
        
        systemConfig.saveConfig();
        System.out.println("✅ 成本控制设置已保存");
    }
    
    /**
     * 修改处理限制
     */
    private static void modifyProcessingLimits() {
        System.out.println("\n🔄 处理限制设置:");
        
        System.out.print("每次最大处理热搜数 (当前: " + systemConfig.getInt("processing.max_trends_per_run") + "): ");
        String maxTrends = scanner.nextLine().trim();
        if (!maxTrends.isEmpty()) {
            systemConfig.set("processing.max_trends_per_run", maxTrends);
        }
        
        System.out.print("请求间隔毫秒 (当前: " + systemConfig.getInt("processing.delay_between_requests") + "): ");
        String delay = scanner.nextLine().trim();
        if (!delay.isEmpty()) {
            systemConfig.set("processing.delay_between_requests", delay);
        }
        
        systemConfig.saveConfig();
        System.out.println("✅ 处理限制设置已保存");
    }
    
    /**
     * 重置为默认配置
     */
    private static void resetToDefaultConfig() {
        System.out.print("确认重置所有配置为默认值? (y/N): ");
        String confirm = scanner.nextLine().trim().toLowerCase();
        
        if ("y".equals(confirm) || "yes".equals(confirm)) {
            systemConfig = new SystemConfig(); // 重新创建会加载默认配置
            systemConfig.saveConfig();
            System.out.println("✅ 配置已重置为默认值");
        } else {
            System.out.println("操作已取消");
        }
    }
    
    /**
     * 查看使用统计
     */
    private static void viewUsageStatistics() {
        System.out.println("\n📈 使用统计:");
        
        try {
            CostControlService costControl = new CostControlService();
            costControl.printUsageStats();
            
            var stats = costControl.getTodayUsageStats();
            
            System.out.println("\n📊 详细分析:");
            System.out.println("- OpenAI使用率: " + String.format("%.1f%%", stats.getOpenAIUsagePercentage()));
            System.out.println("- 成本使用率: " + String.format("%.1f%%", stats.getCostUsagePercentage()));
            System.out.println("- 状态: " + (stats.isNearLimit() ? "⚠️ 接近限制" : "✅ 正常"));
            
            if (stats.openAIRequests > 0) {
                double avgCost = stats.dailyCost / stats.openAIRequests;
                System.out.println("- 平均每次请求成本: $" + String.format("%.3f", avgCost));
            }
            
        } catch (Exception e) {
            System.err.println("❌ 无法获取使用统计: " + e.getMessage());
        }
    }
    
    /**
     * 管理定时任务
     */
    private static void manageScheduledTasks() {
        System.out.println("\n📅 定时任务管理:");
        
        if (taskScheduler == null) {
            System.err.println("❌ 任务调度器未初始化");
            return;
        }
        
        System.out.println("1. 查看当前任务");
        System.out.println("2. 启动调度器");
        System.out.println("3. 停止调度器");
        System.out.println("4. 重启调度器");
        System.out.println("0. 返回主菜单");
        
        System.out.print("请选择操作: ");
        int choice = scanner.nextInt();
        scanner.nextLine();
        
        try {
            switch (choice) {
                case 1 -> {
                    System.out.println("调度器状态: " + (taskScheduler.isRunning() ? "✅ 运行中" : "❌ 已停止"));
                    System.out.println("每日执行时间: " + systemConfig.getSchedulerConfig().getDailyJobTime());
                }
                case 2 -> {
                    if (!taskScheduler.isRunning()) {
                        taskScheduler.startScheduler();
                        System.out.println("✅ 调度器已启动");
                    } else {
                        System.out.println("⚠️ 调度器已在运行中");
                    }
                }
                case 3 -> {
                    taskScheduler.stopScheduler();
                    System.out.println("✅ 调度器已停止");
                }
                case 4 -> {
                    taskScheduler.stopScheduler();
                    Thread.sleep(1000);
                    taskScheduler.startScheduler();
                    System.out.println("✅ 调度器已重启");
                }
                case 0 -> { /* 返回主菜单 */ }
                default -> System.out.println("❌ 无效选择");
            }
        } catch (Exception e) {
            System.err.println("❌ 操作失败: " + e.getMessage());
        }
    }
    
    /**
     * 运行系统诊断
     */
    private static void runDiagnostics() {
        System.out.println("\n🔧 系统诊断:");
        
        // 检查Java版本
        String javaVersion = System.getProperty("java.version");
        System.out.println("Java版本: " + javaVersion);
        
        if (javaVersion.startsWith("24") || javaVersion.startsWith("21")) {
            System.out.println("✅ Java版本兼容");
        } else {
            System.out.println("⚠️ 建议使用Java 21+以获得最佳性能");
        }
        
        // 检查内存使用
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        System.out.println("内存使用: " + (usedMemory / 1024 / 1024) + "MB / " + (totalMemory / 1024 / 1024) + "MB");
        
        // 检查磁盘空间
        java.io.File currentDir = new java.io.File(".");
        long freeSpace = currentDir.getFreeSpace();
        System.out.println("可用磁盘空间: " + (freeSpace / 1024 / 1024 / 1024) + "GB");
        
        // 检查网络连接
        System.out.println("\n🌐 网络连接测试:");
        testNetworkConnectivity();
        
        // 检查文件权限
        System.out.println("\n📁 文件系统检查:");
        checkFilePermissions();
    }
    
    /**
     * 测试网络连接
     */
    private static void testNetworkConnectivity() {
        String[] testUrls = {
            "https://api.openai.com",
            "https://api.twitter.com", 
            "https://sellingpartnerapi-na.amazon.com"
        };
        
        for (String url : testUrls) {
            try {
                java.net.URL testUrl = new java.net.URL(url);
                java.net.HttpURLConnection connection = (java.net.HttpURLConnection) testUrl.openConnection();
                connection.setRequestMethod("HEAD");
                connection.setConnectTimeout(5000);
                connection.setReadTimeout(5000);
                
                int responseCode = connection.getResponseCode();
                if (responseCode < 400) {
                    System.out.println("✅ " + url + " - 连接正常");
                } else {
                    System.out.println("⚠️ " + url + " - 响应码: " + responseCode);
                }
                
            } catch (Exception e) {
                System.out.println("❌ " + url + " - 连接失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 检查文件权限
     */
    private static void checkFilePermissions() {
        String[] testPaths = {
            ".",
            systemConfig.getFileConfig().getImageDirectory(),
            systemConfig.getFileConfig().getQuotaFile()
        };
        
        for (String path : testPaths) {
            java.io.File file = new java.io.File(path);
            System.out.println(path + ":");
            System.out.println("  存在: " + (file.exists() ? "✅" : "❌"));
            System.out.println("  可读: " + (file.canRead() ? "✅" : "❌"));
            System.out.println("  可写: " + (file.canWrite() ? "✅" : "❌"));
        }
    }
    
    /**
     * 显示帮助信息
     */
    private static void showHelp() {
        System.out.println("\n❓ 帮助信息:");
        System.out.println("=".repeat(60));
        System.out.println("🎯 系统功能说明:");
        System.out.println("1. 自动获取Twitter热搜话题");
        System.out.println("2. 使用AI生成讽刺/搞笑风格的T恤设计");
        System.out.println("3. 自动上架商品到Amazon marketplace");
        System.out.println("4. 智能成本控制和配额管理");
        System.out.println("5. 完整的错误处理和重试机制");
        System.out.println();
        System.out.println("🔧 配置文件位置:");
        System.out.println("- 系统配置: system_config.properties");
        System.out.println("- Twitter密钥: C:\\me\\twitter_keys.txt");
        System.out.println("- OpenAI密钥: C:\\me\\key.txt");
        System.out.println("- Amazon配置: C:\\me\\amazon_config.txt");
        System.out.println();
        System.out.println("📊 使用建议:");
        System.out.println("- 首次使用请先测试各个组件");
        System.out.println("- 建议设置合理的每日预算限制");
        System.out.println("- 定期检查使用统计和成本");
        System.out.println("- 遇到问题可运行系统诊断");
        System.out.println("=".repeat(60));
    }
    
    /**
     * 清理资源
     */
    private static void cleanup() {
        try {
            if (taskScheduler != null) {
                taskScheduler.stopScheduler();
            }
            if (scanner != null) {
                scanner.close();
            }
            System.out.println("✅ 资源清理完成");
        } catch (Exception e) {
            System.err.println("⚠️ 资源清理时出错: " + e.getMessage());
        }
    }
}
