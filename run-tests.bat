@echo off
echo 🧪 运行热搜T恤自动化系统测试...
echo.

REM 检查Java版本
java -version
if %errorlevel% neq 0 (
    echo ❌ Java未安装或未配置到PATH
    pause
    exit /b 1
)

echo.
echo 📦 编译项目...
call mvn clean compile test-compile
if %errorlevel% neq 0 (
    echo ❌ 编译失败
    pause
    exit /b 1
)

echo.
echo 🧪 运行单元测试...
call mvn test
if %errorlevel% neq 0 (
    echo ❌ 测试失败
    pause
    exit /b 1
)

echo.
echo ✅ 所有测试通过！

echo.
echo 📋 可用的测试命令:
echo mvn test                                    # 运行所有测试
echo mvn test -Dtest=CostControlServiceTest      # 运行成本控制测试
echo mvn test -Dtest=ProductInfoGeneratorTest    # 运行商品信息生成测试
echo mvn test -Dtest=RetryServiceTest            # 运行重试机制测试
echo mvn test -Dtest=TaskSchedulerTest           # 运行任务调度测试
echo mvn test -Dtest=IntegrationTest             # 运行集成测试
echo.
echo 🔧 运行交互式测试:
echo mvn exec:java -Dexec.mainClass="com.hal.test.EnhancedSystemTest" -Dexec.classpathScope=test

pause
