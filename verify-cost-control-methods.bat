@echo off
echo 🔍 验证CostControlService新增方法...
echo.

echo 📋 检查方法是否存在...
findstr "getMaxTopicsCanProcess" src\main\java\com\hal\service\CostControlService.java >nul
if %errorlevel% equ 0 (
    echo ✅ getMaxTopicsCanProcess方法已添加
) else (
    echo ❌ getMaxTopicsCanProcess方法未找到
)

findstr "canProcessTopics" src\main\java\com\hal\service\CostControlService.java >nul
if %errorlevel% equ 0 (
    echo ✅ canProcessTopics方法已添加
) else (
    echo ❌ canProcessTopics方法未找到
)

findstr "getQuotaInfo" src\main\java\com\hal\service\CostControlService.java >nul
if %errorlevel% equ 0 (
    echo ✅ getQuotaInfo方法已添加
) else (
    echo ❌ getQuotaInfo方法未找到
)

findstr "QuotaInfo" src\main\java\com\hal\service\CostControlService.java >nul
if %errorlevel% equ 0 (
    echo ✅ QuotaInfo类已添加
) else (
    echo ❌ QuotaInfo类未找到
)

echo.
echo 📦 编译验证...
javac -cp "target\classes" src\main\java\com\hal\service\CostControlService.java 2>nul
if %errorlevel% equ 0 (
    echo ✅ CostControlService编译成功
) else (
    echo ❌ CostControlService编译失败
)

echo.
echo 🧪 运行简单测试...
java -cp "src\test\java;target\classes" com.hal.test.CostControlMethodTest 2>nul
if %errorlevel% equ 0 (
    echo ✅ 方法测试通过
) else (
    echo ⚠️ 方法测试需要完整环境
)

echo.
echo 📊 方法添加总结:
echo ✅ getMaxTopicsCanProcess() - 获取可处理的最大话题数（标准质量）
echo ✅ getMaxTopicsCanProcess(boolean isHD) - 获取可处理的最大话题数（指定质量）
echo ✅ canProcessTopics(int topicCount, boolean isHD) - 检查是否可以处理指定数量的话题
echo ✅ getQuotaInfo() - 获取详细的配额信息
echo ✅ QuotaInfo类 - 配额信息数据类
echo ✅ 增强的printUsageStats() - 包含可处理话题数信息
echo.
echo 🎉 CostControlService方法验证完成！

pause
