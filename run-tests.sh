#!/bin/bash

echo "🧪 运行热搜T恤自动化系统测试..."
echo

# 检查Java版本
echo "📋 检查Java版本..."
java -version
if [ $? -ne 0 ]; then
    echo "❌ Java未安装或未配置到PATH"
    exit 1
fi

echo
echo "📦 编译项目..."
mvn clean compile test-compile
if [ $? -ne 0 ]; then
    echo "❌ 编译失败"
    exit 1
fi

echo
echo "🧪 运行单元测试..."
mvn test
if [ $? -ne 0 ]; then
    echo "❌ 测试失败"
    exit 1
fi

echo
echo "✅ 所有测试通过！"

echo
echo "📋 可用的测试命令:"
echo "mvn test                                    # 运行所有测试"
echo "mvn test -Dtest=CostControlServiceTest      # 运行成本控制测试"
echo "mvn test -Dtest=ProductInfoGeneratorTest    # 运行商品信息生成测试"
echo "mvn test -Dtest=RetryServiceTest            # 运行重试机制测试"
echo "mvn test -Dtest=TaskSchedulerTest           # 运行任务调度测试"
echo "mvn test -Dtest=IntegrationTest             # 运行集成测试"
echo
echo "🔧 运行交互式测试:"
echo "mvn exec:java -Dexec.mainClass=\"com.hal.test.EnhancedSystemTest\" -Dexec.classpathScope=test"
