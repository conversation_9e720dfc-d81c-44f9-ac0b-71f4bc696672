@echo off
echo 🚀 启动热搜T恤自动化系统 - 数据库集成版...
echo.

REM 检查Java版本
echo 📋 检查Java版本...
java -version
if %errorlevel% neq 0 (
    echo ❌ Java未安装或未配置到PATH
    echo 💡 请安装JDK 24或更高版本
    pause
    exit /b 1
)

echo.
echo 📋 检查PostgreSQL连接...
REM 这里可以添加PostgreSQL连接检查

echo.
echo 📦 编译项目...
call mvn clean compile
if %errorlevel% neq 0 (
    echo ❌ 编译失败
    pause
    exit /b 1
)

echo.
echo ✅ 编译成功，启动数据库集成版应用...
echo.

REM 启动数据库集成版主应用
call mvn exec:java -Dexec.mainClass="com.hal.DatabaseIntegratedTrendTShirtApplication"

pause
