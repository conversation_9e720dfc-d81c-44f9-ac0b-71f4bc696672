#!/bin/bash

echo "🚀 启动热搜T恤自动化系统 - Web应用版..."
echo

# 检查Java版本
echo "📋 检查Java版本..."
java -version
if [ $? -ne 0 ]; then
    echo "❌ Java未安装或未配置到PATH"
    echo "💡 请安装JDK 24或更高版本"
    exit 1
fi

echo
echo "📋 检查PostgreSQL连接..."
# 这里可以添加PostgreSQL连接检查

echo
echo "📦 编译项目..."
mvn clean compile
if [ $? -ne 0 ]; then
    echo "❌ 编译失败"
    exit 1
fi

echo
echo "✅ 编译成功，启动Web应用..."
echo
echo "🌐 应用将在以下地址启动:"
echo "   - 主页: http://localhost:8080/api"
echo "   - API文档: http://localhost:8080/api/swagger-ui.html"
echo "   - 健康检查: http://localhost:8080/api/actuator/health"
echo

# 启动Spring Boot应用
mvn spring-boot:run
