#!/bin/bash

echo "🚀 启动热搜T恤自动化系统 - 数据库集成版..."
echo

# 检查Java版本
echo "📋 检查Java版本..."
java -version
if [ $? -ne 0 ]; then
    echo "❌ Java未安装或未配置到PATH"
    echo "💡 请安装JDK 24或更高版本"
    exit 1
fi

echo
echo "📋 检查PostgreSQL连接..."
# 这里可以添加PostgreSQL连接检查

echo
echo "📦 编译项目..."
mvn clean compile
if [ $? -ne 0 ]; then
    echo "❌ 编译失败"
    exit 1
fi

echo
echo "✅ 编译成功，启动数据库集成版应用..."
echo

# 启动数据库集成版主应用
mvn exec:java -Dexec.mainClass="com.hal.DatabaseIntegratedTrendTShirtApplication"
