@echo off
echo 🚀 启动热搜T恤自动化系统 - Gemini版 (JDK 8兼容)...
echo.

REM 检查Java版本
echo 📋 检查Java版本...
java -version
if %errorlevel% neq 0 (
    echo ❌ Java未安装或未配置到PATH
    echo 💡 请安装JDK 8或更高版本
    pause
    exit /b 1
)

echo.
echo 📋 验证JDK 8兼容性...
java -version 2>&1 | findstr "1.8" >nul
if %errorlevel% equ 0 (
    echo ✅ 检测到JDK 8，完全兼容
) else (
    echo ⚠️ 检测到非JDK 8版本，项目已兼容JDK 8+
)

echo.
echo 📋 检查Gemini API密钥...
if "%GEMINI_API_KEY%"=="" (
    echo ⚠️ 未设置GEMINI_API_KEY环境变量
    echo 💡 请设置您的Gemini API密钥:
    echo    set GEMINI_API_KEY=your-gemini-api-key
    echo.
    echo 🔧 或者在application.yml中配置
) else (
    echo ✅ Gemini API密钥已配置
)

echo.
echo 📋 检查PostgreSQL连接...
REM 这里可以添加PostgreSQL连接检查

echo.
echo 📦 编译项目...
call mvn clean compile -DskipTests
if %errorlevel% neq 0 (
    echo ❌ 编译失败
    pause
    exit /b 1
)

echo.
echo ✅ 编译成功，启动Gemini版Web应用...
echo.
echo 🌐 应用将在以下地址启动:
echo    - 主页: http://localhost:8080/api
echo    - API文档: http://localhost:8080/api/swagger-ui.html
echo    - 健康检查: http://localhost:8080/api/actuator/health
echo.
echo 💰 成本优势:
echo    - Gemini API: $0.0025/图片
echo    - OpenAI API: $0.04-$0.08/图片
echo    - 节省成本: 93.75%% - 96.875%%
echo.

REM 启动Spring Boot应用
call mvn spring-boot:run -Dspring.profiles.active=gemini

pause
