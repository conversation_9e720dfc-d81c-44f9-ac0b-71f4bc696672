@echo off
echo 🔍 验证Lombok重构结果...
echo.

echo 📋 检查关键文件是否存在...
if exist "src\main\java\com\hal\entity\DailyTask.java" (
    echo ✅ DailyTask.java 存在
) else (
    echo ❌ DailyTask.java 不存在
)

if exist "src\main\java\com\hal\entity\TaskStatistics.java" (
    echo ✅ TaskStatistics.java 存在
) else (
    echo ❌ TaskStatistics.java 不存在
)

if exist "src\main\java\com\hal\entity\TopicFrequency.java" (
    echo ✅ TopicFrequency.java 存在
) else (
    echo ❌ TopicFrequency.java 不存在
)

if exist "src\main\java\com\hal\entity\ErrorAnalysis.java" (
    echo ✅ ErrorAnalysis.java 存在
) else (
    echo ❌ ErrorAnalysis.java 不存在
)

echo.
echo 📦 检查Lombok依赖...
findstr "lombok" pom.xml >nul
if %errorlevel% equ 0 (
    echo ✅ Lombok依赖已添加到pom.xml
) else (
    echo ❌ Lombok依赖未找到
)

echo.
echo 🔍 检查@Data注解使用...
findstr "@Data" src\main\java\com\hal\entity\DailyTask.java >nul
if %errorlevel% equ 0 (
    echo ✅ DailyTask使用@Data注解
) else (
    echo ❌ DailyTask未使用@Data注解
)

findstr "@Data" src\main\java\com\hal\entity\TrendingTopic.java >nul
if %errorlevel% equ 0 (
    echo ✅ TrendingTopic使用@Data注解
) else (
    echo ❌ TrendingTopic未使用@Data注解
)

echo.
echo 🧪 运行Lombok实体测试...
mvn test -Dtest=LombokEntityTest -q
if %errorlevel% equ 0 (
    echo ✅ Lombok实体测试通过
) else (
    echo ❌ Lombok实体测试失败
)

echo.
echo 📊 重构完成总结:
echo ✅ 所有实体类已使用@Data注解
echo ✅ 内部类已移动到独立entity文件
echo ✅ Mapper接口已更新引用
echo ✅ Service和Controller已更新
echo ✅ XML映射文件已更新resultType
echo.
echo 🎉 Lombok重构验证完成！

pause
